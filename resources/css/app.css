@tailwind base;
@tailwind components;
@tailwind utilities;


@layer components {

    .wrapper {
        margin: 0;
        padding: 0;
        display: flex;
        height: 100vh;
        overflow: hidden;
        position: relative;
        @apply flex-col items-center
    }

    .container {
        background-color: #ccc;
        border-radius: 50%;
        border: 15px solid #dde;
        position: relative;
        overflow: hidden;
        transition: 15s;
        @apply w-80 h-80 md:w-[500px] md:h-[500px]
    }

    .stroke {
        @apply absolute z-50 max-w-max w-[calc(100%+30px)] -left-[15px] -top-[15px] pointer-events-none
    }

    .container div {
        height: 50%;
        position: absolute;
        clip-path: polygon(100% 0, 50% 100%, 0 0);
        transform: translateX(-50%);
        transform-origin: bottom;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        font-weight: bold;
        font-family: sans-serif;
        color: #fff;
        @apply w-1/2 left-[73px] md:w-[200px] md:left-[135px]
    }

    .container .one {
        background-color: #3f51b5;
        left: 50%;
    }

    .container .two {
        background-color: #ff9800;
        transform: rotate(45deg);
    }

    .container .three {
        background-color: #e91e63;
        transform: rotate(90deg);
    }

    .container .four {
        background-color: #4caf50;
        transform: rotate(135deg);
    }

    .container .five {
        background-color: #009688;
        transform: rotate(180deg);
    }

    .container .six {
        background-color: #795548;
        transform: rotate(225deg);
    }

    .container .seven {
        background-color: #9c27b0;
        transform: rotate(270deg);
    }

    .container .eight {
        background-color: #f44336;
        transform: rotate(315deg);
    }

    .arrow {
        position: absolute;
        top: -30px;
        left: 50%;
        transform: translateX(-50%);
        color: #fff;
        z-index: 9999;
    }



    #spin {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 10;
        background-color: #e2e2e2;
        text-transform: uppercase;
        font-weight: bold;
        color: #a2a2a2;
        font-family: sans-serif;
        border-radius: 50%;
        cursor: pointer;
        outline: none;
        letter-spacing: 1px;
        @apply md:w-[80px] md:h-[80px] w-14 h-14 border
    }
}

@keyframes rotate {
    100% {
        transform: rotate(360deg);
    }
}