<div x-data="{
    prize: $wire.entangle('prize').live,
    openDialog: $wire.entangle('showDialog').live,
    spins: $wire.entangle('spins').live,
    prizes: @js($prizes),
    parts: ['one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight'],
    ordered: [],
    spinning: false,
    getPrizeIcon(index) {
        return this.ordered[index] ? this.ordered[index].wheel_prize_type.icon : 'fa-solid fa-ban text-xl';
    },
    getValue(index) {
        let value = this.ordered[index] ? this.ordered[index].value : 'حظ أوفر';
        if (this.ordered[index]?.wheel_prize_type?.slug == 'cash_back') {
            value += '%'
        }
        if (value == 0) return ''
        return value
    },
    getText(index) {
        item = this.ordered[index]
        if (item) {
            if (item.wheel_prize_type.slug != 'money')
                return item.wheel_prize_type.name
        }
        return ''
    },
    init() {
    
        this.ordered = [this.prizes[0], this.prizes[7], this.prizes[6], this.prizes[5], this.prizes[4], this.prizes[3], this.prizes[2], this.prizes[1]];
        let container = document.querySelector('.container');
        let btn = document.getElementById('spin');
        let values = [0, 1, 2, 3, 4, 5, 6, 7]
        let degree = 0
        let winPercent = []
        for (const item of this.prizes) {
            let percent = Math.ceil(8 * (item.win_percent / 100))
            index = this.prizes.findIndex(i => i.id == item.id)
            for (let i = 0; i < percent; i++) {
                winPercent.push(index)
            }
        }

        let prizes = this.prizes
       
        let spinning = this.spinning
        const setSpinningOf = () => {
            this.spinning = false;
        }
            let spins = this.spins

        btn.onclick = function() {
            if (spins <= 0 || spinning) return
            spinning = true
            var result = Math.ceil(Math.random() * 7)
            console.log(result)
            var divider = 360 / values.length - 1;
            var offset = divider / 2;
            container.style.transition = '0s'
            container.style.transform = '';
            setTimeout(() => {
                container.style.transition = '10s'
                var dataRotation = 1800 + (45 * winPercent[result]) //Math.ceil(Math.random() * 1800 + 1080)
                degree = dataRotation
                var number = Math.floor(Math.ceil((degree + offset) % 360) / divider);
                container.style.transform = `rotate(${degree}deg)`;
                console.log(prizes[number])
                @this.checkPrize(number)
                setTimeout(() => {
                    @this.checkDialog()
                    setSpinningOf()
                }, 10000)
            }, 5)
        }
    },

}">
    <div x-cloak x-show="openDialog" style="z-index: 999999999"
        class="fixed w-full min-h-screen bg-black/50 flex justify-center items-center" x-transition.opacity>
        <div x-show="openDialog" @click.outside="openDialog = false"
            class="bg-gray-800 p-5 min-w-[50%] text-center rounded-xl text-white" x-transition>
            <h1 class="text-center font-bold text-xl text-white">مبروك</h1>
            <p class="mt-5">لقد ربحت:
            </p>
            <p class="mt-2">
                <span>{{ $prize?->wheelPrizeType->name }}</span>
                <span>{{ $this->getValue() }}</span>
            </p>
        </div>
    </div>
    <div class="flex flex-col items-center gap-10 relative justify-center z-50 w-full p-10 bg-cover text-white bg-no-repeat bg-center min-h-screen"
        style="background-image: url('{{ $background }}')">
        <div class="absolute top-0 left-0 w-full h-full bg-black/25"></div>
        <div class="px-4 py-2 rounded-lg bg-black/50 h-fit text-white">
            <h1 class="text-center font-bold text-xl text-white">عجلة الفرصة</h1>
        </div>
        <div class="wrapper overflow-visible">
            <div wire:ignore class="relative h-fit">
                <button id="spin" class="z-50">
                    @if ($btn)
                        <img src="{{ $btn }}" alt="" class="w-full">
                    @else
                        Spin
                    @endif
                </button>
                <div class="arrow">
                    <img src="{{ $pin }}" alt="" class="w-16 h-16">
                </div>
                @if ($stroke)
                    <img src="{{ $stroke }}" alt="" class="stroke">
                @endif
                <div class="container">
                    <template x-for="(item,index) in parts">
                        <div :class="item"
                            :style="{ backgroundColor: index % 2 == 0 ? @js($primaryColor) : @js($secondaryColor) }">
                            <span class="pl-5 -rotate-90 flex items-center gap-1 text-sm font-bold">
                                <span x-text="getText(index)"></span>
                                <span x-text="getValue(index)"></span>
                                <i class="text-xl" :class="getPrizeIcon(index)"></i>
                            </span>
                        </div>
                    </template>

                </div>
            </div>
            <div class="w-full text-center mt-5 p-3 rounded-lg bg-black/50">
                
                    <p>لديك {{$spins}} لفة مجانية</p>
            
            </div>
            @if (isset($settings['wheel_min_charge']))
            <div class="w-full text-right mt-2 p-3 rounded-lg bg-black/50">
                    <h1>شروط الحصول على لفة</h1>
                    <ul>
                <li>شحن مبلغ {{ number_format($settings['wheel_min_charge']) }} ليرة علي الآقل يومياً</li>
                    </ul>
                </div>
            @endif

        </div>

    </div>

</div>
