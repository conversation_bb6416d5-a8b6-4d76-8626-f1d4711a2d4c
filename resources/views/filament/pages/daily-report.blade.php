<x-filament-panels::page>
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 hover:shadow-md transition-shadow duration-200">
        <h2 class="text-xl font-bold mb-4">{{ __('Daily Report') }}</h2>
        
        <form wire:submit="generateReport" class="space-y-6">
            {{ $this->form }}
            
            <div class="flex justify-end">
                <x-filament::button type="submit">
                    {{ __('Send to Telegram') }}
                </x-filament::button>
            </div>
        </form>
        
        <div class="mt-8">
            <h3 class="text-lg font-semibold mb-4">{{ __('Report Information') }}</h3>
            
            <div class="prose dark:prose-invert max-w-none">
                <p>{{ __('The daily report includes the following information:') }}</p>
                
                <ul>
                    <li>{{ __('Charge requests summary') }}</li>
                    <li>{{ __('Withdraw requests summary') }}</li>
                    <li>{{ __('Cashback summary') }}</li>
                    <li>{{ __('Wheel prizes summary') }}</li>
                    <li>{{ __('User statistics') }}</li>
                    <li>{{ __('Financial summary') }}</li>
                </ul>
                
                <p>{{ __('The report will be sent to the Telegram reports group configured in the system.') }}</p>
            </div>
        </div>
    </div>
</x-filament-panels::page>
