<x-filament-panels::page>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- Quick Actions Card -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 hover:shadow-md transition-shadow duration-200">
            <h2 class="text-xl font-bold mb-4">{{ __('Quick Actions') }}</h2>
            <div class="grid grid-cols-2 gap-4">
                <a href="{{ route('filament.admin.resources.charge-requests.index') }}" class="flex flex-col items-center justify-center p-4 bg-primary-50 dark:bg-primary-950/50 rounded-lg hover:bg-primary-100 dark:hover:bg-primary-900/50 transition-colors duration-200">
                    <x-heroicon-o-currency-dollar class="w-8 h-8 text-primary-500 mb-2" />
                    <span class="text-sm font-medium text-center">{{ __('Charge Requests') }}</span>
                </a>
                <a href="{{ route('filament.admin.resources.withdraw-requests.index') }}" class="flex flex-col items-center justify-center p-4 bg-primary-50 dark:bg-primary-950/50 rounded-lg hover:bg-primary-100 dark:hover:bg-primary-900/50 transition-colors duration-200">
                    <x-heroicon-o-credit-card class="w-8 h-8 text-primary-500 mb-2" />
                    <span class="text-sm font-medium text-center">{{ __('Withdraw Requests') }}</span>
                </a>
                <a href="{{ route('filament.admin.resources.users.index') }}" class="flex flex-col items-center justify-center p-4 bg-primary-50 dark:bg-primary-950/50 rounded-lg hover:bg-primary-100 dark:hover:bg-primary-900/50 transition-colors duration-200">
                    <x-heroicon-o-users class="w-8 h-8 text-primary-500 mb-2" />
                    <span class="text-sm font-medium text-center">{{ __('Users') }}</span>
                </a>
                <a href="{{ route('filament.admin.resources.coupons.index') }}" class="flex flex-col items-center justify-center p-4 bg-primary-50 dark:bg-primary-950/50 rounded-lg hover:bg-primary-100 dark:hover:bg-primary-900/50 transition-colors duration-200">
                    <x-heroicon-o-gift class="w-8 h-8 text-primary-500 mb-2" />
                    <span class="text-sm font-medium text-center">{{ __('Coupons') }}</span>
                </a>
            </div>
        </div>

        <!-- System Status Card -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 hover:shadow-md transition-shadow duration-200">
            <h2 class="text-xl font-bold mb-4">{{ __('System Status') }}</h2>
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium">{{ __('Maintenance Mode') }}</span>
                    <span class="px-3 py-1 text-xs font-medium rounded-full {{ config('app.debug') ? 'bg-amber-100 text-amber-800 dark:bg-amber-900/50 dark:text-amber-300' : 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300' }}">
                        {{ config('app.debug') ? __('Enabled') : __('Disabled') }}
                    </span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium">{{ __('Environment') }}</span>
                    <span class="px-3 py-1 text-xs font-medium rounded-full {{ config('app.env') === 'production' ? 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300' : 'bg-amber-100 text-amber-800 dark:bg-amber-900/50 dark:text-amber-300' }}">
                        {{ ucfirst(config('app.env')) }}
                    </span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium">{{ __('Locale') }}</span>
                    <span class="px-3 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-300">
                        {{ strtoupper(app()->getLocale()) }}
                    </span>
                </div>
            </div>
        </div>

        <!-- Daily Report Card -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 hover:shadow-md transition-shadow duration-200">
            <h2 class="text-xl font-bold mb-4">{{ __('Daily Report') }}</h2>
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium">{{ __('Date') }}</span>
                    <input type="date" class="text-sm rounded-lg border-gray-300 dark:border-gray-700 dark:bg-gray-900" value="{{ date('Y-m-d') }}" id="report-date">
                </div>
                <div class="flex items-center justify-center">
                    <button id="generate-report" class="px-4 py-2 text-sm font-medium text-white bg-primary-600 rounded-lg hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-primary-700 dark:hover:bg-primary-600">
                        {{ __('Generate Report') }}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const generateReportBtn = document.getElementById('generate-report');
            const reportDateInput = document.getElementById('report-date');

            generateReportBtn.addEventListener('click', function() {
                const date = reportDateInput.value;
                if (date) {
                    window.location.href = `/api/v1/logs/daily?date=${date}`;
                }
            });
        });
    </script>
</x-filament-panels::page>
