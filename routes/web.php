<?php

use App\Http\Controllers\Api\V1\LogsController;
use App\Http\Middleware\AllowTelegramIPs;
use App\Http\Middleware\IsValidTelegramRequest;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
});
Route::middleware('throttle:10,1')->group(function () {
    Route::get('/wheel/{id}', \App\Livewire\Wheel::class)->name('wheel');
});

// API Routes
Route::prefix('api')->group(function () {
    Route::prefix('v1')->group(function () {
        // Daily report endpoint
        Route::get('/logs/daily', [LogsController::class, 'index']);
    });
});
