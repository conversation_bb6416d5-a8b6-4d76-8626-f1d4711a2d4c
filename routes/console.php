<?php

use App\Facades\Cashier;
use App\Facades\Logs;
use App\Jobs\DatabaseBackupJob;
use App\Services\ReferralCalculationService;
use Illuminate\Support\Facades\Schedule;


Schedule::call(function () {
    $refService = new ReferralCalculationService();
    $refService->calculate();
})->daily();

Schedule::call(fn() => Logs::sendDailyLogs())->daily();
Schedule::call(fn() => Logs::clearPaymentMessages())->daily();
Schedule::command('app:process-wheel-cashback')->daily();
Schedule::job(new DatabaseBackupJob())->daily();
// Schedule::call(fn() => Logs::sendWeeklyLogs())->weekly();
// Schedule::call(fn() => Logs::sendMonthlyLogs())->monthly();
