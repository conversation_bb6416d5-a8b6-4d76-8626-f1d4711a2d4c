<?php

use App\Http\Controllers\Api\V1\AuthController;
use App\Http\Controllers\Api\V1\PaymentWebhookController;
use App\Http\Controllers\Api\V1\UserController;
use App\Http\Controllers\Api\V1\WebhookController;
use App\Http\Middleware\AdminMiddleware;
use Illuminate\Support\Facades\Route;

Route::prefix('v1')->middleware('throttle:60,1')->group(function () {
  //  Route::post('webhooks/{token}', WebhookController::class)->name('webhooks');
  //     Route::post('test', [WebhookController::class, 'test']);
  Route::post('payment', PaymentWebhookController::class);
  Route::get('test', [PaymentWebhookController::class, 'test']);

  //     Route::post('login', [AuthController::class, 'login']);
  //     Route::post('sendError', [PaymentWebhookController::class, 'sendErrorMessage']);
  //     Route::middleware('auth:api')->group(function () {
  //         Route::apiResource('users', UserController::class)->middleware(AdminMiddleware::class);
  //    });
});
