stages:
    - deploy
    - v2

before_script:
    - "command -v ssh-agent >/dev/null || ( apt-get update -y && apt-get install openssh-client -y )"
    - eval $(ssh-agent -s)
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - chmod 400 "$SSH_KEY"
    - ssh-add "$SSH_KEY"
deploy:
    stage: deploy
    script:
        - ssh -o StrictHostKeyChecking=no -i $SSH_KEY root@************** "
          cd /var/www/html &&
          cd elias &&
          git pull origin main &&
          php artisan migrate &&
          php artisan optimize:clear &&
          php artisan optimize &&
          cd ../got &&
          git pull origin main &&
          php artisan migrate &&
          php artisan optimize:clear &&
          php artisan optimize &&
          cd ../arrab &&
          git pull origin main &&
          php artisan migrate &&
          php artisan optimize:clear &&
          php artisan optimize &&
          cd ../sambot &&
          git pull origin main &&
          php artisan migrate &&
          php artisan optimize:clear &&
          php artisan optimize &&
          cd ../diamon &&
          git pull origin main &&
          php artisan migrate &&
          php artisan optimize:clear &&
          php artisan optimize &&
          
          cd ../jafar &&
          git pull origin main &&
          php artisan migrate &&
          php artisan optimize:clear &&
          php artisan optimize &&
          cd ../othman &&
          git pull origin main &&
          php artisan migrate &&
          php artisan optimize:clear &&
          php artisan optimize &&
          cd ../boos &&
          git pull origin main &&
          php artisan migrate &&
          php artisan optimize:clear &&
          php artisan optimize &&
          cd ../barcelona &&
          git pull origin main &&
          php artisan migrate &&
          php artisan optimize:clear &&
          php artisan optimize &&
          cd ../davinci &&
          git pull origin main &&
          php artisan migrate &&
          php artisan optimize:clear &&
          php artisan optimize &&
          cd ../fox &&
          git pull origin main &&
          php artisan migrate &&
          php artisan optimize:clear &&
          php artisan optimize &&
          cd ../secret &&
          git pull origin main &&
          php artisan migrate &&
          php artisan optimize:clear &&
          php artisan optimize &&
          cd ../golden &&
          git pull origin main &&
          php artisan migrate &&
          php artisan optimize:clear &&
          php artisan optimize &&
          cd ../scream &&
          git pull origin main &&
          php artisan migrate &&
          php artisan optimize:clear &&
          php artisan optimize &&
          cd ../dahab &&
          git pull origin main &&
          php artisan migrate &&
          php artisan optimize:clear &&
          php artisan optimize &&
          cd ../liverpol &&
          git pull origin main &&
          php artisan migrate &&
          php artisan optimize:clear &&
          php artisan optimize &&
          cd ../zeus &&
          git pull origin main &&
          php artisan migrate &&
          php artisan optimize:clear &&
          php artisan optimize &&
          cd ../elite &&
          git pull origin main &&
          php artisan migrate &&
          php artisan optimize:clear &&
          php artisan optimize &&
          cd ../teacher &&
          git pull origin main &&
          php artisan migrate &&
          php artisan optimize:clear &&
          php artisan optimize &&
          cd ../jad &&
          git pull origin main &&
          php artisan migrate &&
          php artisan optimize:clear &&
          php artisan optimize 
         
          "
    rules:
        - if: '$CI_COMMIT_BRANCH == "main"'