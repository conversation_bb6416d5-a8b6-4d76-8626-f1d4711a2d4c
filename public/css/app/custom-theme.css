@import '../../../../vendor/filament/filament/resources/css/theme.css';

:root {
    --font-family: '<PERSON><PERSON><PERSON>', sans-serif;
}

/* Custom styles for RTL support */
.fi-sidebar-header {
    @apply rtl:pr-4 rtl:pl-0;
}

.fi-sidebar-item-icon {
    @apply rtl:ml-3 rtl:mr-0;
}

.fi-sidebar-group-label {
    @apply rtl:pr-4 rtl:pl-0;
}

.fi-dropdown-panel {
    @apply rtl:right-0 rtl:left-auto;
}

/* Improve form elements */
.fi-input-wrapper {
    @apply rounded-lg;
}

.fi-btn {
    @apply rounded-lg transition-all duration-200;
}

.fi-btn-primary {
    @apply hover:shadow-md;
}

/* Improve tables */
.fi-ta-header-cell {
    @apply bg-gray-50 dark:bg-gray-800;
}

.fi-ta-row {
    @apply hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors duration-200;
}

/* Improve cards */
.fi-section {
    @apply rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200;
}

/* Improve navigation */
.fi-sidebar-nav {
    @apply bg-white dark:bg-gray-900;
}

.fi-sidebar-item {
    @apply rounded-lg transition-colors duration-200;
}

.fi-sidebar-item-active {
    @apply bg-primary-50 dark:bg-primary-950/50;
}

/* Improve dashboard widgets */
.fi-widget {
    @apply rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200;
}

/* Improve form layout */
.fi-form-section {
    @apply rounded-xl;
}

/* Improve notifications */
.fi-notification {
    @apply rounded-xl shadow-lg;
}

/* Improve modal */
.fi-modal-window {
    @apply rounded-xl;
}

/* Improve pagination */
.fi-pagination-item {
    @apply rounded-lg;
}

/* Improve select */
.fi-select-trigger {
    @apply rounded-lg;
}

/* Improve toggle */
.fi-toggle {
    @apply transition-all duration-200;
}

/* Improve tabs */
.fi-tabs-item {
    @apply rounded-t-lg;
}
