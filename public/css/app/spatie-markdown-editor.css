.cm-s-easymde .cm-header-1 {
    font-size: 1.875rem
}
.cm-s-easymde .cm-header-2 {
    font-size: 1.5rem
}
.cm-s-easymde .cm-header-3 {
    font-size: 1.25rem
}
.cm-s-easymde .cm-header-4 {
    font-size: 1.125rem
}
.cm-s-easymde .cm-header-5 {
    font-size:1.125rem
}
.cm-s-easymde .cm-header-6 {
    font-size:1rem
}
.cm-s-easymde .cm-comment {
    background: none;
}
.cm-keyword {color: #708;}
.cm-atom {color: #219;}
.cm-number {color: #164;}
.cm-def {color: #00f;}
.cm-variable,
.cm-punctuation,
.cm-property,
.cm-operator {}
.cm-variable-2 {color: #05a;}
.cm-formatting-list, .cm-formatting-list + .cm-variable-2 {color: #000;}
.cm-variable-3, .cm-s-default .cm-type {color: #085;}
.cm-comment {color: #a50;}
.cm-string {color: #a11;}
.cm-string-2 {color: #f50;}
.cm-meta {color: #555;}
.cm-qualifier {color: #555;}
.cm-builtin {color: #30a;}
.cm-bracket {color: #997;}
.cm-tag {color: #170;}
.cm-attribute {color: #00c;}
.cm-hr {color: #999;}
.cm-link {color: #00c;}

/* Remove the .EasyMDEContainer button mask images set by filament in favour of font awesome icons */
.spatie-filament-markdown-editor .EasyMDEContainer .editor-toolbar button::before {
    mask: unset;
    width: unset;
    height: unset;
    background: unset;
}
