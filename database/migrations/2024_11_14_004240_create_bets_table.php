<?php

use App\Models\IchancyAccount;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bets', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(IchancyAccount::class)->constrained()->cascadeOnDelete();
            $table->string('bet_id');
            $table->integer('bet_type');
            $table->string('date');
            $table->integer('is_live');
            $table->float('price');
            $table->integer('events_count');
            $table->string('bet_status');
            $table->float('win_amount');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bets');
    }
};
