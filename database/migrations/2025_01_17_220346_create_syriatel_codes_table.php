<?php

use App\Enums\SyriatelCodeType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('syriatel_codes', function (Blueprint $table) {
            $table->id();
            $table->string('code');
            $table->string('phone');
            $table->enum('type', SyriatelCodeType::toArray());
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('syriatel_codes');
    }
};
