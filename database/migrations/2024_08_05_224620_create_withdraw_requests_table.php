<?php

use App\Enums\CommessionType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('withdraw_requests', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(\App\Models\User::class)->constrained()->cascadeOnDelete();
            $table->foreignIdFor(\App\Models\WithdrawMethod::class)->constrained()->cascadeOnDelete();
            $table->float('amount');
            $table->string('payment_info');
            $table->enum('status', ['pending', 'approved', 'rejected', 'canceled'])->default('pending');
            $table->text('message')->nullable();
            $table->string('operation_id')->unique();
            $table->boolean('is_charged')->default(false);
            $table->float('commession')->default(0);
            $table->string('message_id')->nullable();
            $table->enum('commession_type', array_column(CommessionType::cases(), 'value'))->default('fixed');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('withdraw_requests');
    }
};
