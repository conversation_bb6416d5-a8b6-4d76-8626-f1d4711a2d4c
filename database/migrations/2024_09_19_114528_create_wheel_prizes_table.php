<?php

use App\Models\WheelPrizeType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('wheel_prizes', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(WheelPrizeType::class)->constrained()->cascadeOnDelete();
            $table->string('name')->nullable();
            $table->string('value');
            $table->boolean('is_active')->default(false);
            $table->float('win_percent')->default(0);
            $table->integer('sort')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('wheel_prizes');
    }
};
