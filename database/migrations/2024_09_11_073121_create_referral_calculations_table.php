<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('referral_calculations', function (Blueprint $table) {
            $table->id();
            $table->integer('total_referrals')->default(0);
            $table->integer('total_active_referrals')->default(0);
            $table->float('total_profit')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('referral_calculations');
    }
};
