<?php

use App\Models\User;
use App\Models\WheelPrize;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('wheel_cashback_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(User::class)->constrained()->cascadeOnDelete();
            $table->foreignIdFor(WheelPrize::class)->constrained()->cascadeOnDelete();
            $table->decimal('percentage', 8, 2)->comment('The cashback percentage from the wheel prize');
            $table->decimal('base_amount', 15, 2)->nullable()->comment('The base amount for calculation');
            $table->decimal('cashback_amount', 15, 2)->nullable()->comment('The calculated cashback amount');
            $table->boolean('is_processed')->default(false);
            $table->timestamp('processed_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('wheel_cashback_logs');
    }
};
