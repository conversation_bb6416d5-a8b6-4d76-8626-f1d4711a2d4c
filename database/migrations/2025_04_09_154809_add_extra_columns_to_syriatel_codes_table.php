<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('syriatel_codes', function (Blueprint $table) {
            $table->float('amount')->default(0);
            $table->float('max_amount')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('syriatel_codes', function (Blueprint $table) {
            //
        });
    }
};
