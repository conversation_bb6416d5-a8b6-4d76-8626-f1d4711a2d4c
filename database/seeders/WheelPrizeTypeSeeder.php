<?php

namespace Database\Seeders;

use App\Models\WheelPrizeType;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class WheelPrizeTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        WheelPrizeType::query()->updateOrCreate(
            [

                'slug' => 'cash_back',
            ],
            [
                'name' => "كاش باك",
                'icon' => 'fa-solid fa-sack-dollar'
            ]
        );
        WheelPrizeType::query()->updateOrCreate(
            [

                'slug' => 'free_spin',
            ],
            [
                'name' => 'لفة مجانية',
                'icon' => 'fa-solid fa-rotate'
            ]
        );
        WheelPrizeType::query()->updateOrCreate(
            [

                'slug' => 'money',
            ],
            [
                'name' => 'رصيد مجاني',
                'icon' => 'fa-solid fa-gift'
            ]
        );
        WheelPrizeType::query()->updateOrCreate(
            [

                'slug' => 'hard_luck',
            ],
            [
                'name' => 'حظ أوفر',
                'icon' => 'fa-solid fa-ban'
            ]
        );
    }
}
