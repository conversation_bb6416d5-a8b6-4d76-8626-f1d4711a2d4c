<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Setting::query()
            ->updateOrCreate([
                'key' => 'greeting_message'
            ], ['value' => 'أهلا وسهلاً بك هذه قائمة بالخيارات المتاحة']);
        Setting::query()
            ->updateOrCreate([
                'key' => 'charge_message'
            ], ['value' => 'اختر احد طرق الشحن التالية']);
        Setting::query()
            ->updateOrCreate([
                'key' => 'withdraw_message'
            ], ['value' => 'اختر احد طرق السحب التالية']);
        Setting::query()
            ->updateOrCreate([
                'key' => 'admin_group_id'
            ], ['value' => '-4264189511']);
        Setting::query()
            ->updateOrCreate([
                'key' => 'captcha_enabled'
            ], ['value' => 0]);
    }
}
