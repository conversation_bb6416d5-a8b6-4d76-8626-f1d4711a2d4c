<?php

namespace Database\Seeders;

use App\Models\WithdrawMethod;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class WithdrawMethodSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $syriatelCash = WithdrawMethod::firstOrCreate([
            'name' => 'سيرياتل كاش',
            'slug' => 'syriatel_cash',
            'is_active' => true
        ]);
        $bemo = WithdrawMethod::firstOrCreate([
            'name' => 'بيمو',
            'slug' => 'bemo',
            'is_active' => true
        ]);
        $mtnCash = WithdrawMethod::firstOrCreate([
            'name' => 'mtn كاش',
            'slug' => 'mtn_cash',
            'is_active' => true
        ]);

        $syriatelCash->withdrawInfos()->firstOrCreate([
            'key' => 'phone',
            'value' => 'أدخل الرقم المراد التحويل اليه',
        ]);
        $mtnCash->withdrawInfos()->firstOrCreate([
            'key' => 'phone',
            'value' => 'أدخل الرقم المراد التحويل اليه',
        ]);
        $bemo->withdrawInfos()->firstOrCreate([
            'key' => 'account',
            'value' => 'أدخل رقم الحساب المراد التحويل اليه'
        ]);
    }
}
