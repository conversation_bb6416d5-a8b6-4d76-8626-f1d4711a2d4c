<?php

namespace Database\Seeders;

use App\Models\PaymentMethod;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class PaymentMethodSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $syriatelCash = PaymentMethod::firstOrCreate([
            'name' => 'سيرياتل كاش',
            'slug' => 'syriatel_cash',
            'is_active' => true
        ]);

        $bemo = PaymentMethod::firstOrCreate([
            'name' => 'بيمو',
            'slug' => 'bemo',
            'is_active' => true
        ]);
        $mtnCash = PaymentMethod::firstOrCreate([
            'name' => 'mtn كاش',
            'slug' => 'mtn_cash',
            'is_active' => true
        ]);
        $syriatelCash->paymentInfos()
            ->firstOrCreate([
                'key' => 'phone',
                'value' => 'قم بالتحويل الى الرقم التالي عن طريق تطبيق أقرب إليك "دفع يدوي" `***********` ثم أدخل رقم عملية التحويل',
            ]);
        $mtnCash->paymentInfos()
            ->firstOrCreate([
                'key' => 'phone',
                'value' => 'قم بالتحويل الى الرقم التالي عن طريق تطبيق أقرب إليك "دفع يدوي" `***********` ثم أدخل رقم عملية التحويل',
            ]);

        $bemo->paymentInfos()
            ->firstOrCreate([
                'key' => 'account',
                'value' => 'يرجى تحويل المبلغ الى الحساب التالي *********** ثم ادخل الحساب الذي تم  التحويل منه'
            ]);
    }
}
