<?php

namespace App\Channels;

use App\Facades\Bot;
use App\Facades\BotMessage;
use App\Facades\Settings;
use Illuminate\Support\Facades\Log;

class TelegramChannel
{

    public function send($notifiable, $notification)
    {
        $token = Settings::get('telegram_token');
        $text = $notification->message;
        $message = BotMessage::setChat($notifiable->telegram_id)
            ->setText($text)
            ->toArray();
        Bot::setToken($token)
            ->sendMessage($message);
    }
}
