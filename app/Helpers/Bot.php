<?php

namespace App\Helpers;

use App\BotApi\Connectors\TelegramConnector;
use App\BotApi\Requests\CheckChannelMemberRequest;
use App\BotApi\Requests\EditMessageButtonsRequest;
use App\BotApi\Requests\EditMessageRequest;
use App\BotApi\Requests\GetBotInfoRequest;
use App\BotApi\Requests\SendMessageRequest;
use App\BotApi\Requests\SetBotDescriptionRequest;
use App\BotApi\Requests\SetBotNameRequest;
use App\BotCommands\AccountCommand;
use App\BotCommands\StartCommand;
use App\Services\SettingService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Telegram\Bot\Api;

class Bot
{

    protected $token;
    protected Api $api;
    protected TelegramConnector $connector;
    public function __construct()
    {
        $token = config('ichancy.telegram_token');
        if ($token) {
            $this->token = $token;
            $this->connector = new TelegramConnector($this->token);
            $this->api = new Api($token);
            Cache::rememberForever('bot_token', fn() => $token);
        }

        if (!$this->token) {
            $settingsService = app()->make(SettingService::class);
            $token = $settingsService->getToken();
            if ($token) {
                $this->setToken($token);
            }
        }
    }
    /**
     * Sets the token and creates a new instance of the Api class using the token.
     *
     * @param string $token The token used to authenticate the Api instance.
     */
    public function setToken(string $token)
    {
        // $this->token = $token;
        // $this->connector = new TelegramConnector($this->token);
        // $this->api = new Api($token);
        // Cache::rememberForever('bot_token', fn() => $token);
        return $this;
    }

    /**
     * Handles the commands received from the Telegram API.
     *
     * This method iterates over the entities in the message received from the
     * Telegram API and checks if the entity is a bot command. If it is, it
     * creates a new instance of the corresponding command class and calls the
     * handle method on it.
     *
     * @param Request $request The request sent by the Telegram API.
     * @return void
     */
    public function handleCommands()
    {
        $request = request();
        $message = $request['message'];
        $parts = explode(' ', $message['text']);
        $command = $parts[0];
        $entities = $message['entities'];
        foreach ($entities as $entity) {
            if ($entity['type'] == 'bot_command') {
                /*
                 * Matches the command received from the Telegram API to the
                 * corresponding command class. If the command is not
                 * recognized, it defaults to null.
                 */
                $handler = match ($command) {
                    '/start' => new StartCommand($this, $message),
                    '/account' => new AccountCommand($this, $message),
                    default => null
                };
                if ($handler) {
                    /*
                     * Creates a new instance of the command class and calls the
                     * handle method on it.
                     */
                    $handler->handle();
                }
            }
        }
    }
    public function setCommands(array $params): bool
    {
        return $this->api->setMyCommands($params);
    }
    public function removeCommands(array $params = []): bool
    {
        return $this->api->deleteMyCommands($params);
    }
    /**
     * Sends a message to the user.
     *
     * @param array $params The parameters for the message.
     * @return void
     */
    public function sendMessage(array | BotMessage $params): array //\Telegram\Bot\Objects\Message
    {
        $data = $params;
        if ($params instanceof BotMessage) {
            $data = $params->toArray();
        }
        $request = new SendMessageRequest($data);
        /*
         * Sends a message to the user using the Telegram Api.
         *
         * @param array $params The parameters for the message.
         */
        $response = $this->connector->send($request);
        return $response->json();
    }

    /**
     * Edits a message sent by the bot.
     *
     * @param array $params The parameters for the message.
     *                     Should contain 'chat_id', 'message_id', and 'text'.
     * @return void
     */
    public function editMessage(array | BotMessage $params): array
    {
        $data = $params;
        if ($params instanceof BotMessage) {
            $data = $params->toArray();
        }
        /*
         * Edits a message sent by the bot using the Telegram Api.
         *
         * @param array $params The parameters for the message.
         *                     Should contain 'chat_id', 'message_id', and 'text'.
         */
        $request = new EditMessageRequest($data);
        $response = $this->connector->send($request);
        return $response->json();
    }
    public function editMessageReplyMarkup(array | BotMessage $params): array
    {
        $data = $params;
        if ($params instanceof BotMessage) {
            $data = $params->toArray();
        }
        $request = new EditMessageButtonsRequest($data);
        $response = $this->connector->send($request);
        return $response->json();
    }
    public function setBotName(string $name)
    {
        $connector = new TelegramConnector($this->token);
        $request  = new SetBotNameRequest($name);
        $response  = $connector->send($request);
        return $response->json();
    }
    public function setBotDescription(string $description)
    {
        $connector = new TelegramConnector($this->token);
        $request = new SetBotDescriptionRequest($description);
        $response = $connector->send($request);
        return $response->json();
    }
    public function getChatMember(string $channelId, string $userId): mixed
    {
        $connector = new TelegramConnector($this->token);
        $request = new CheckChannelMemberRequest(['chat_id' => $channelId, 'user_id' => $userId]);
        $response = $connector->send($request);
        return $response->json();
    }
    public function getBotInfo(): array
    {
        $connector = new TelegramConnector($this->token);
        $request = new GetBotInfoRequest();
        $response = $connector->send($request);
        return $response->json();
    }
}
