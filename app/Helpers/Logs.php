<?php

namespace App\Helpers;

use App\Facades\Bot;
use App\Facades\BotMessage;
use App\Facades\Settings;
use App\Models\DailyLog;
use App\Models\PaymentMessage;
use App\Models\UserBalance;
use App\Services\ChargeRequestService;
use App\Services\CouponLogService;
use App\Services\DailyLogService;
use App\Services\WithdrawRequestService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Number;

class Logs
{
    protected ChargeRequestService $chargeRequestService;
    protected WithdrawRequestService $withdrawRequestService;
    protected CouponLogService $couponLogService;

    public function __construct()
    {
        $this->chargeRequestService = app()->make(ChargeRequestService::class);
        $this->withdrawRequestService = app()->make(WithdrawRequestService::class);
        $this->couponLogService = app()->make(CouponLogService::class);
    }
    public function getLogs($filters)
    {
        $totalCharge = $this->chargeRequestService->getTotalChargeAmount($filters);
        $totalWithdraw = $this->withdrawRequestService->getTotalWithdraw($filters);

        $burn = $totalCharge - $totalWithdraw;
        $charge = Number::format($totalCharge);
        $withdraw = Number::format($totalWithdraw);
        $burn = Number::format($burn);
        $couponLogs = Number::format($this->couponLogService->getLogs($filters));
        return compact('charge', 'withdraw', 'burn', 'couponLogs');
    }
    public function sendDailyLogs()
    {
        app(DailyLogService::class)->dailyLog(); //$this->getLogs(['time' => 'yesterday']);
    }
    public function sendWeeklyLogs()
    {
        $logs = $this->getLogs(['time' => 'last_week']);
        $this->send($logs, 'تقرير الجرد الاسبوعي');
    }
    public function sendMonthlyLogs()
    {
        $logs = $this->getLogs(['time' => 'last_month']);
        $this->send($logs, 'تقرير الجرد الشهري');
    }
    public function send($logs, $title)
    {
        $token = Settings::get('telegram_token');
        if (!$token) return;
        $reciever = Settings::get('send_logs_to');
        if (!$reciever) return;
        $message = BotMessage::setChat($reciever)
            ->setText("
***{$title}***

***مجموع الشحن***: {$logs['charge']} ليرة
***مجموع السحب***: {$logs['withdraw']} ليرة
***مجموع الحرق***: {$logs['burn']} ليرة
***إجمالي اكواد الهدايا:*** {$logs['couponLogs']} ليرة
        ")->setParseMode('MarkdownV2');
        Bot::setToken($token)->sendMessage($message->toArray());
    }

    public function clearPaymentMessages()
    {
        PaymentMessage::whereDate('created_at', '<', now()->yesterday())
            ->delete();
    }
}
