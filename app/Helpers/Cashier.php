<?php

namespace App\Helpers;

use App\BotApi\Connectors\CashierConnector;
use App\BotApi\Connectors\LoginConnector;
use App\BotApi\Enum\MoneyTransferType;
use App\BotApi\Requests\CreateUserRequest;
use App\BotApi\Requests\DepositRequest;
use App\BotApi\Requests\GetPlayerBalanceRequest;
use App\BotApi\Requests\GetPlayersRequest;
use App\BotApi\Requests\GetWalletRequest;
use App\BotApi\Requests\LoginRequest;
use App\BotApi\Requests\WithdrawRequest;
use App\Facades\Settings;
use App\Jobs\CreateUserJob;
use App\Jobs\TransferMoneyJob;
use App\Models\Setting;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Queue;

class Cashier
{
    protected CashierConnector $connector;
    public function __construct()
    {
        $cookie = Settings::get('cookies');
        $this->connector = new CashierConnector($cookie);
    }

    /**
     * Get the wallet balance from the cashier API.
     *
     * @return mixed The wallet balance data in JSON format.
     */
    public function getWallet(): mixed
    {
        // Create a new instance of the GetWalletRequest class.
        $request = new GetWalletRequest();

        return $this->sendRequest($request);
    }

    /**
     * Create a user in the cashier API.
     *
     * @param array $data The data for creating the user.
     * @return mixed The response from the cashier API in JSON format.
     */
    public function createUser(array $data, string $userTelegramId): mixed
    {
        $captchaEnabled = Cache::get('captcha_enabled', fn() => Setting::where('key', 'captcha_enabled')->value('value') ?? 0);
        if ($captchaEnabled == 1) {
            if (isset($data['login']))
                $data['username'] = $data['login'];

            Queue::push(new CreateUserJob($data, $userTelegramId));
            return true;
        }
        // Create a new instance of the CreateUserRequest class with the provided data.
        $request = new CreateUserRequest($data);

        // Send the request to the cashier API and return the response in JSON format.
        return $this->sendRequest($request);
    }

    /**
     * Get players statistics from the cashier API.
     *
     * @return mixed The players statistics data in JSON format.
     */
    public function getPlayers(array $filter = []): mixed
    {
        // Create a new instance of the GetPlayersRequest class.
        $request = new GetPlayersRequest($filter);

        // Send the request to the cashier API and return the response in JSON format.
        return $this->sendRequest($request);
    }
    public function getPlayerDetails(string $username): array|null
    {
        $request = new GetPlayersRequest([
            'players' => $username
        ]);
        $response = $this->sendRequest($request);
        if (isset($response['result']['records']) && !empty($response['result']['records'])) {
            return $response['result']['records'][0];
        }
        return null;
    }
    public function getPlayerBalanceById(string $playerId): int
    {
        $request = new GetPlayerBalanceRequest($playerId);
        $response = $this->sendRequest($request);
        if (isset($response['result']) && is_array($response['result']) && !empty($response['result'])) {
            return $response['result'][0]['balance'];
        }
        return 0;
    }
    /**
     * Deposit money to a player in the cashier API.
     *
     * @param string $playerId The ID of the player to deposit to.
     * @param string $amount The amount of money to deposit.
     * @return mixed The response from the cashier API in JSON format.
     */
    public function depositToPlayer(string $playerId, string $amount): mixed
    {
        // Create a new instance of the DepositRequest class with the provided player ID and amount.
        $request = new DepositRequest([
            'amount' => $amount, // The amount of money to deposit.
            'playerId' => $playerId // The ID of the player to deposit to.
        ]);

        // Send the request to the cashier API and return the response in JSON format.
        return $this->sendRequest($request);
    }
    public function depositToPlayerByUsername(string $amount, string $userTelegramId): void
    {
        Queue::push(new TransferMoneyJob(MoneyTransferType::DEPOSIT, $amount, $userTelegramId));
    }
    public function withdrawToPlayerByUsername(string $amount, string $userTelegramId): void
    {
        Queue::push(new TransferMoneyJob(MoneyTransferType::WITHDRAW, $amount, $userTelegramId));
    }
    /**
     * Withdraw money from a player in the cashier API.
     *
     * @param string $playerId The ID of the player to withdraw from.
     * @param string $amount The amount of money to withdraw.
     * @return mixed The response from the cashier API in JSON format.
     */
    public function withdrawFromPlayer(string $playerId, string $amount): mixed
    {
        // Create a new instance of the WithdrawRequest class with the provided player ID and amount.
        $request = new WithdrawRequest([
            'amount' => $amount, // The amount of money to withdraw.
            'playerId' => $playerId // The ID of the player to withdraw from.
        ]);

        // Send the request to the cashier API and return the response in JSON format.
        return $this->sendRequest($request);
    }
    public function login(): mixed
    {
        $connector = new LoginConnector();
        $request = new LoginRequest();
        $response = $connector->send($request);

        $cookie = $response->headers()->get('set-cookie');
        $cookies = [];
        foreach ($cookie as $item) {
            $parts = explode(';', $item);
            if (
                str_starts_with($parts[0], 'PHPSESSID')
                || str_starts_with($parts[0], 'languageCode')
                || str_starts_with($parts[0], '__cf_bm')
            ) {
                $cookies[] = $parts[0];
            }
        }
        Settings::set('cookies', implode(';', $cookies));
        return $response->json();
    }

    protected function sendRequest($request)
    {
        $response = $this->connector->send($request);
        if (!$response->successful()) {
            Settings::set('captcha_enabled', 1);
            return;
        }
        $data = $response->json();
        if (isset($data['result']) && $data['result'] == 'ex') {
            $this->login();
            $this->connector->send($request);
        }
        if ($response->successful())
            return $response->json();
        return [];
    }
}
