<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Log;
use Telegram\Bot\Api;
use Telegram\Bot\Laravel\Facades\Telegram;

class BotMessage
{
    protected $buttons;
    protected $rows;
    protected $text;
    protected $chatId;
    protected $messageId;
    protected $parseMode = null;
    protected $entities = [];
    protected $reply_parametrs = [];

    public function __construct() {}
    /**
     * Adds a button to the bot interface.
     *
     * This method allows adding a button with specified text and optional callback data to the bot's interface.
     *
     * @param string $text The text displayed on the button.
     * @param array $callbackData The data that will be sent back to the server when the button is clicked.
     * @return $this Returns the instance of this class to allow for method chaining.
     */
    public function addButton(string $text, string | array $callbackData = [], ?string $url = null)
    {
        // Add the button with the provided text and callback data to the buttons array.
        $button = [
            'text' => $text,
            'callback_data' => $callbackData
        ];
        if ($url) {
            $button['url'] = $url;
        }
        $this->buttons[] = $button;
        return $this;
    }
    /**
     * Adds a row of buttons to the bot interface.
     *
     * This method is used to separate the buttons in the bot interface into rows. It works by adding the current
     * buttons array to the rows array, and then resetting the buttons array.
     *
     * @return $this Returns the instance of this class to allow for method chaining.
     */
    public function addRow()
    {
        // Add the current buttons array to the rows array.
        $this->rows[] = $this->buttons;

        // Reset the buttons array to prepare it for the next row.
        $this->buttons = [];

        return $this;
    }
    public function addEntity(array $entity)
    {
        $this->entities[] = $entity;
        return $this;
    }
    /**
     * Sets the text of the message.
     *
     * This method is used to set the text of the message that will be sent to the user. It takes a string as an
     * argument and stores it in the $text property of this class.
     *
     * @param string $text The text of the message.
     * @return $this Returns the instance of this class to allow for method chaining.
     */
    public function setText(string $text)
    {
        // Store the text of the message in the $text property of this class.
        if ($this->parseMode == 'MarkdownV2') {
            $text = str_replace(
                ['\\', '[', ']', '(', ')', '~', '>', '#', '+', '-', '=', '{', '}', '.', '!'],
                ['\\\\', '\\[', '\\]', '\\(', '\\)', '\\~', '\\>', '\\#', '\\+', '\\-', '\\=', '\\{', '\\}', '\\.', '\\!'],
                $text
            );
        }

        $this->text = $text;

        return $this;
    }
    /**
     * Sets the chat ID of the message.
     *
     * This method is used to set the chat ID of the message that will be sent to the user. It takes a string as an
     * argument and stores it in the $chatId property of this class. This is used to specify the chat that the message
     * will be sent to.
     *
     * @param string $chatId The chat ID of the message.
     * @return $this Returns the instance of this class to allow for method chaining.
     */
    public function setChat(string $chatId)
    {
        // Store the chat ID of the message in the $chatId property of this class.
        $this->chatId = $chatId;

        return $this;
    }

    /**
     * Sets the message ID of the message.
     *
     * This method is used to set the message ID of the message that will be sent to the user. It takes a string as an
     * argument and stores it in the $messageId property of this class.
     *
     * @param string $messageId The message ID of the message.
     * @return $this Returns the instance of this class to allow for method chaining.
     */
    public function setMessageId(string $messageId)
    {
        // Store the message ID of the message in the $messageId property of this class.
        $this->messageId = $messageId;

        return $this;
    }
    public function setParseMode(?string $parseMode = null)
    {
        $this->parseMode = $parseMode;
        return $this;
    }
    public function setReplyParameters(array $reply_parametrs)
    {
        $this->reply_parametrs = $reply_parametrs;
        return $this;
    }
    public function addQoute(string $text)
    {
        $this->reply_parametrs[] = ['qoute' => $text];
        return $this;
    }
    /**
     * Converts the message to an array that can be sent to the Telegram API.
     *
     * This method converts all of the information that has been set by the other methods of this class into an array
     * that can be sent to the Telegram API. It handles setting the text of the message, the buttons, and the keyboard.
     *
     * @return array The message data as an array.
     */
    public function toArray()
    {
        $data = [];
        // Initialize the data array which will hold all of the information that will be sent to the Telegram API.
        if ($this->parseMode)
            $data['parse_mode'] = $this->parseMode;

        // If a chat ID has been set, add it to the data array.
        if ($this->chatId) {
            $data['chat_id'] = $this->chatId;
        }

        if ($this->messageId) {
            $data['message_id'] = $this->messageId;
        }
        // If the text of the message has been set, add it to the data array.
        if ($this->text) {
            $data['text'] = $this->text;
        }

        // If buttons have been set, add a row of buttons to the message and reset the buttons array.
        if ($this->buttons) {
            $this->addRow();
        }

        // If there are rows of buttons, add them to the data array.
        if ($this->rows) {
            $data['reply_markup'] = json_encode([
                'inline_keyboard' => $this->rows
            ]);
        }
        if (!empty($this->reply_parametrs))
            $data['reply_parameters'] = $this->reply_parametrs;
        // Return the message data as an array.
        return $data;
    }
}
