<?php

namespace App\Helpers;


class ApiResponse
{

    public static function success($data = [])
    {
        return response()->json(['message' => 'success', 'data' => $data], 200);
    }
    public static function forbidden($message = 'forbidden')
    {
        return response()->json(['message' => $message], 403);
    }
    public static function unAuthenticated($message = 'un-authenticated')
    {

        return response()->json(['message' => $message], 401);
    }
}
