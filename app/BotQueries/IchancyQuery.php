<?php

namespace App\BotQueries;

use App\BotQueries\Concerns\HandleQueryConcern;
use App\Facades\Bot;
use App\Facades\BotMessage;

class IchancyQuery implements HandleQueryConcern
{

    public static function handle(array $params = []): void
    {

        $message = BotMessage::setChat($params['from']['id'])
            ->setMessageId($params['message']['message_id'])
            ->setText('اختر أحد الخيارات التالية')
            ->addButton('إنشاء حساب ⚡🆕', 'register')
            ->addRow()
            ->addButton('   شحن رصيد 📥   ', 'ichancy_charge')
            ->addButton('   سحب رصيد 📤   ', 'ichancy_withdraw')
            ->addRow()
            ->addButton('حسابي 👤', 'my_account')
            ->addRow()
            ->addButton('رجوع ⬅', 'back_to_start');
        Bot::editMessage($message->toArray());
    }
}
