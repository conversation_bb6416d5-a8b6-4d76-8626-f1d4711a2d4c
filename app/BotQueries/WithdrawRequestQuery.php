<?php

namespace App\BotQueries;

use App\BotQueries\Concerns\HandleQueryConcern;
use App\Facades\Bot;
use App\Facades\BotMessage;
use App\Facades\Settings;
use App\Models\User;
use App\Services\UserService;
use Illuminate\Support\Facades\Cache;

class WithdrawRequestQuery implements HandleQueryConcern
{

    public static function handle(array $params = []): void
    {
        $userService = app()->make(UserService::class);
        $query = $params['query'];
        $userTelegramId = $query['from']['id'];
        $withdrawMethod = $params['withdraw_method'];

        if ($withdrawMethod->withdrawInfos->isEmpty()) return;

        $stepId = 0;

        $step = $withdrawMethod->withdrawInfos[0] ?? null;

        if (!$step) return;

        $user = $userService->getByTelegramId($userTelegramId);
        if (!$user) return;
        $text = $step->value;
        $minWithdraw = Settings::get('min_withdraw_amount');
        if ($minWithdraw) {
            $text .= '
*يرجى الانتباه أن الحد الأدنى للسحب هو: ' . $minWithdraw . '*';
        }
        $message = BotMessage::setChat($userTelegramId)
            ->setMessageId($query['message']['message_id'])
            ->setText($text)
            ->toArray();
        Bot::editMessage($message);
        Cache::forget('withdraw_request_' . $userTelegramId);
        Cache::remember('withdraw_request_' . $userTelegramId, 600, fn() => [
            'user' => $userTelegramId,
            'step_id' => $stepId,
            'withdraw_request' => [
                'withdraw_method_id' => $withdrawMethod->id,
                'user_id' => $user->id,
                'commession' => $withdrawMethod->commession,
                'commession_type' => $withdrawMethod->commession_type
            ]
        ]);
    }
}
