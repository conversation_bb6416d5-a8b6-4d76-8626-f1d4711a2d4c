<?php

namespace App\BotQueries;

use App\BotQueries\Concerns\HandleQueryConcern;
use App\Facades\Bot;
use App\Facades\BotMessage;
use App\Services\UserService;

class MyAccountQuery implements HandleQueryConcern
{

    public static function handle(array $params = []): void
    {
        $userService = app()->make(UserService::class);
        $user = $userService->getByTelegramId($params['from']['id']);

        if (!$user) return;

        $account = $user->ichancyAccount;
        $balance = $user->userBalance;
        if (!$account) {
            $message = BotMessage::setChat($user->telegram_id)
                ->setMessageId($params['message']['message_id'])
                ->setText('عذراً لاتمتلك حساب, يمكنك انشاء حساب من خيار انشاء حساب في البوت');
            Bot::editMessage($message->toArray());
            return;
        }

        $message = BotMessage::setChat($user->telegram_id)
            ->setMessageId($params['message']['message_id']);
        $total = 0;
        if ($balance) {
            $total = $balance->active;
        }
        $text = 'تفاصيل الحساب
الدخول: `' . $account->username . '`
كلمة المرور: `' . $account->password . '`
الرصيد: `' . $total . '` ليرة ';
        $message->setText($text);
        Bot::editMessage($message);
    }
}
