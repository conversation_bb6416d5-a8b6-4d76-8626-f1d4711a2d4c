<?php

namespace App\BotQueries;

use App\BotQueries\Concerns\HandleQueryConcern;
use App\Facades\Bot;
use App\Facades\BotMessage;
use App\Services\PaymentMethodService;

class ChargeQuery implements HandleQueryConcern
{

    public static function handle(array $params = []): void
    {
        $paymentMethodService = app()->make(PaymentMethodService::class);
        $paymentMethds = $paymentMethodService->activeMethods();

        $message = BotMessage::setChat($params['from']['id'])
            ->setMessageId($params['message']['message_id'])
            ->setText('اختر طريقة الدفع');

        foreach ($paymentMethds as $method) {
            $message = $message->addButton($method->name, 'payment_method_' . $method->id)
                ->addRow();
        }

        $message = $message->addButton('رجوع ⬅', 'back_to_start');
        Bot::editMessage($message->toArray());
    }
}
