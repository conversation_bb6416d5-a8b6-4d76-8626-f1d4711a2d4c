<?php

namespace App\BotQueries;

use App\BotQueries\Concerns\HandleQueryConcern;
use App\Enums\CommessionType;
use App\Facades\Bot;
use App\Facades\BotMessage;
use App\Facades\Settings;
use App\Services\WithdrawMethodService;
use Illuminate\Support\Facades\Log;

class WithdrawQuery implements HandleQueryConcern
{

    public static function handle(array $params = []): void
    {
        $withdrawMethodService = app()->make(WithdrawMethodService::class);
        $withdrawMethods = $withdrawMethodService->activeMethods();

        $message = BotMessage::setChat($params['from']['id'])
            ->setMessageId($params['message']['message_id'])
            ->setText('اختر طريقة السحب');

        foreach ($withdrawMethods as $method) {
            $methodName = $method->name;
            if ($method->commession && $method->commession_type) {
                $methodName .= ' (العمولة :' . $method->commession;
                if ($method->commession_type->value == CommessionType::PERCENT->value) {
                    $methodName .= '%';
                } else {
                    $methodName .= 'ل.س';
                }
                $methodName .= ')';
            }
            $message = $message->addButton($methodName, 'withdraw_method_' . $method->id)
                ->addRow();
        }

        $message = $message->addButton('رجوع ⬅', 'back_to_start');
        Bot::editMessage($message->toArray());
    }
}
