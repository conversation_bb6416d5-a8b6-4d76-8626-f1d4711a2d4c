<?php

namespace App\BotQueries\Ichancy;

use App\BotQueries\Concerns\HandleQueryConcern;
use App\Facades\Bot;
use App\Facades\BotMessage;
use App\Facades\Cashier;
use App\Facades\Settings;
use App\Services\UserService;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class WithdrawIchancyQuery implements HandleQueryConcern
{

    public static function handle(array $params = []): void
    {
        $userService = app()->make(UserService::class);
        $userId = $params['from']['id'];
        $withdrawIchancy = Cache::get('withdraw_ichancy_' . $userId);
        if (!$withdrawIchancy) {
            $message = BotMessage::setChat($userId)
                ->setText('أدخل المبلغ المراد سحبه');
            Bot::sendMessage($message->toArray());
            Cache::remember('withdraw_ichancy_' . $userId, 600, fn() => [
                'amount' => 0
            ]);
            return;
        }
        if (!isset($params['text'])) return;
        $message = BotMessage::setChat($userId)
            ->setText('جار معالجة الطلب يرجى الانتظار');
        Bot::sendMessage($message->toArray());
        $user = $userService->getByTelegramId($userId);

        if (!$user->ichancyAccount) {
            $message = BotMessage::setChat($userId)
                ->setText('يرجى اانشاء حساب اولاً');
            Bot::sendMessage($message->toArray());
            Cache::forget('withdraw_ichancy_' . $userId);
            return;
        }
        if (!is_numeric($params['text'])) {
            $message = BotMessage::setChat($userId)
                ->setText('يرجى إدخال رقم صحيح');
            Bot::sendMessage($message->toArray());
            return;
        }
        if (!$user) return;
        // if captcha is enabled or user has no player id then use queue
        // and python bot to perform the request
        $captcha_enabled = Settings::get('captcha_enabled');
        if (!$user->ichancyAccount->player_id || $captcha_enabled == 1) {
            Cashier::withdrawToPlayerByUsername($params['text'], $user->telegram_id);
            Cache::forget('withdraw_ichancy_' . $userId);
            return;
        }
        $amount = $params['text'];
        // if captcha is enabled or user has no player id then use queue
        // and python bot to perform the request
        $captcha_enabled = Settings::get('captcha_enabled');
        if (!$user->ichancyAccount->player_id || $captcha_enabled == 1) {
            Cashier::withdrawToPlayerByUsername($amount, $user->telegram_id);
            Cache::forget('withdraw_ichancy_' . $user->telegram_id);
            return;
        }
        $playerBalance = Cashier::getPlayerBalanceById($user->ichancyAccount->player_id);
        if ($params['text'] > $playerBalance) {
            $message = BotMessage::setChat($userId)
                ->setText('لا يمكنك سحب هذا المبلغ ليس لديك رصيد كافي');
            Bot::sendMessage($message->toArray());
            Cache::forget('withdraw_ichancy_' . $userId);
            return;
        }
        $response = Cashier::withdrawFromPlayer($user->ichancyAccount->player_id, floatval(-$params['text']));
        if (isset($response['result']) && is_array($response['result']) && !empty($response['result'])) {
            $message = BotMessage::setChat($userId)
                ->setText('تم سحب المبلغ بنجاح الى رصيدك في البوت');
            Bot::sendMessage($message->toArray());
            $user->userBalance->active += $params['text'];
            $user->userBalance->save();
            Cache::forget('withdraw_ichancy_' . $userId);
            return;
        }

        $message = BotMessage::setChat($userId)
            ->setText('حدث خطأ ما يرجى المحاولة لاحقاً');
        Bot::sendMessage($message->toArray());
        Cache::forget('withdraw_ichancy_' . $userId);
    }
}
