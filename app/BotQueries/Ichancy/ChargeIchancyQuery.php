<?php

namespace App\BotQueries\Ichancy;

use App\BotQueries\Concerns\HandleQueryConcern;
use App\Facades\Bot;
use App\Facades\BotMessage;
use App\Facades\Cashier;
use App\Facades\Settings;
use App\Services\UserService;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class ChargeIchancyQuery implements HandleQueryConcern
{

    public static function handle(array $params = []): void
    {
        $userService = app()->make(UserService::class);
        $user = $userService->getByTelegramId($params['from']['id']);
        if (!$user) return;
        $ichancyCharge = Cache::get('ichancy_charge_' . $user->telegram_id);
        if (!$ichancyCharge) {
            Cache::remember('ichancy_charge_' . $user->telegram_id, 600, fn() => [
                'amount' => 0
            ]);
            $message = BotMessage::setChat($params['from']['id'])
                ->setText('أدخل المبلغ المراد شحنه');
            Bot::sendMessage($message->toArray());
            return;
        }
        if (!isset($params['text'])) return;
        $message = BotMessage::setChat($user->telegram_id)
            ->setText('جار معالجة الطلب يرجى الانتظار');
        Bot::sendMessage($message->toArray());
        if (!$user->ichancyAccount) {
            $message = BotMessage::setChat($user->telegram_id)
                ->setText('يرجى اانشاء حساب اولاً');

            Bot::sendMessage($message->toArray());
            Cache::forget('ichancy_charge_' . $user->telegram_id);
            return;
        }
        if (!is_numeric($params['text'])) {
            $message = BotMessage::setChat($params['from']['id'])
                ->setText('يرجى إدخال رقم صحيح');
            Bot::sendMessage($message->toArray());
            return;
        }
        $amount = $params['text'];
        if ($amount > ($user->userBalance?->active ?? 0)) {
            Cache::forget('ichancy_charge_' . $user->telegram_id);
            $message = BotMessage::setChat($params['from']['id'])
                ->setText('لايوجد لديك رصيد كافي')
                ->addButton('شحن رصيد في البوت 📥', 'charge');
            Bot::sendMessage($message->toArray());
            return;
        }
        // if captcha is enabled or user has no player id then use queue
        // and python bot to perform the request
        $captcha_enabled = Settings::get('captcha_enabled');
        if (!$user->ichancyAccount->player_id || $captcha_enabled == 1) {
            Cashier::depositToPlayerByUsername($amount, $user->telegram_id);
            Cache::forget('ichancy_charge_' . $user->telegram_id);
            return;
        }

        $response = Cashier::depositToPlayer($user->ichancyAccount->player_id, $amount);
        Cache::forget('ichancy_charge_' . $user->telegram_id);
        if (isset($response['result']) && $response['result'] != false) {
            $user->userBalance()->update([
                'active' => $user->userBalance->active - $amount
            ]);
            $message = BotMessage::setChat($params['from']['id'])
                ->setText('تم شحن المبلغ بنجاح');
            Bot::sendMessage($message->toArray());
            return;
        }
        $message = BotMessage::setChat($params['from']['id'])
            ->setText('حدث خطأ, يرجى المحاولة لاحقاً');
        Bot::sendMessage($message->toArray());
    }
}
