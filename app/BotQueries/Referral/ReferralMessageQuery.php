<?php

namespace App\BotQueries\Referral;

use App\BotQueries\Concerns\HandleQueryConcern;
use App\Facades\Bot;
use App\Facades\BotMessage;
use App\Facades\Settings;

class ReferralMessageQuery implements HandleQueryConcern
{

    public static function handle($params = []): void
    {
        $referralMessage = Settings::get('referral_message');
        $referralDays = Settings::get('referral_calculation');
        if (empty($referralMessage)) {
            $referralMessage = '
            كن وكيلاً معنا بأبسط طريقة
إحصل على نسبة ثابتة لكل عمليات الشحن والتعبئة القادمة عن طريق رابط احالتك ضمن البوت 
.....
1-عند الدخول الى البوت قم بنسخ رابط الاحالة الخاص بك عن طريق الضغط على خيار رابط الاحالة الخاص بي
2- عندما تقوم بنشر رابط احالتك ويقوم أحد بالتسجيل عن طريقة سنبدأ بحساب نسبة ثابتة لجميع عمليات السحب والتعبئة عن طريقك . 
3-يمكن الاطلاع على عدد الاحالات التي قامت بالتسجيل من خلال الرابط الخاص بك عن طريق الضغط على خيار عدد الاحالات الخاصة بك خلال المسابقة الحالية 
4- يتم حساب الارباح عند وجود 3 إحالات نشطة او أكثر
ماذا تنتظر...!

*ملاحظة*: يتم احتساب ارباح الإحالة كل ' . ($referralDays ?? 10) . ' أيام';
        }
        $message = BotMessage::setChat($params['from']['id'])
            ->setText($referralMessage);
        Bot::sendMessage($message->toArray());
    }
}
