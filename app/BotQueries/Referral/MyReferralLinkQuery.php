<?php

namespace App\BotQueries\Referral;

use App\BotQueries\Concerns\HandleQueryConcern;
use App\Facades\Bot;
use App\Facades\BotMessage;
use Illuminate\Support\Facades\Log;

class MyReferralLinkQuery implements HandleQueryConcern
{

    public static function handle(array $params = []): void
    {
        $info = Bot::getBotInfo();
        $info = $info['result'];
        $message = BotMessage::setChat($params['from']['id'])
            ->setText('`https://t.me/' . $info['username'] . '?start=' . $params['from']['id'] . '`');
        Bot::sendMessage($message);
    }
}
