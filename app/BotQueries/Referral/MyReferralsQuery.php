<?php

namespace App\BotQueries\Referral;

use App\BotQueries\Concerns\HandleQueryConcern;
use App\Facades\Bot;
use App\Facades\BotMessage;
use App\Services\UserService;

class MyReferralsQuery implements HandleQueryConcern
{

    public static function handle(array $params = []): void
    {
        $userService = app()->make(UserService::class);
        $user = $userService->getByTelegramId($params['from']['id']);
        if (!$user) return;
        $referrals = $user->referrals()
            ->whereHas('ichancyAccount')
            ->count();
        $text = '*عدد الإحالات الفعالة* : ' . $referrals;
        $message = BotMessage::setChat($params['from']['id'])
            ->setText($text);
        Bot::sendMessage($message->toArray());
    }
}
