<?php

namespace App\BotQueries\Referral;

use App\BotQueries\Concerns\HandleQueryConcern;
use App\Facades\Bot;
use App\Facades\BotMessage;

class ReferralQuery implements HandleQueryConcern
{

    public static function handle(array $params = []): void
    {
        $message = BotMessage::setChat($params['from']['id'])
            ->setMessageId($params['message']['message_id'])
            ->setText(' نظام الإحالات ')
            ->addButton(' رابط الإحالة الخاص بي ', 'referral_link')
            ->addRow()
            ->addButton('شرح الإحالة', 'referral_description')
            ->addButton('إحالاتي', 'my_referrals')
            ->addRow()
            ->addButton('رجوع ⬅', 'back_to_start')
            ->addRow();

        Bot::editMessage($message->toArray());
    }
}
