<?php

namespace App\BotQueries;

use App\BotQueries\Concerns\HandleQueryConcern;
use App\Facades\Bot;
use App\Facades\BotMessage;
use App\Facades\Settings;
use App\Models\PaymentMethod;
use App\Models\User;
use App\Services\UserService;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class ChargeRequestQuery implements HandleQueryConcern
{

    public static function handle(array $params = []): void
    {
        $userService = app()->make(UserService::class);
        $query = $params['query'];
        $userTelegramId = $query['from']['id'];
        $paymentMethod = $params['payment_method'];

        if ($paymentMethod->paymentInfos->isEmpty()) return;

        $stepId = 0;

        $step = $paymentMethod->paymentInfos[0] ?? null;

        if (!$step) return;
        $user = $userService->getByTelegramId($userTelegramId);
        if (!$user) return;
        $text = $step->value;
        $minCharge = Settings::get('min_charge_amount');
        if ($minCharge) {
            $text .= '
*علماً أن الحد الأدنى للشحن هو: ' . $minCharge . ' وأي قيمة أقل من هذا المبلغ لا يمكن شحنها او استرجاعها*';
        }
        $message = BotMessage::setChat($userTelegramId)
            ->setMessageId($query['message']['message_id'])
            ->setText($text)
            ->toArray();
        Bot::editMessage($message);
        Cache::forget('charge_request_' . $userTelegramId);
        Cache::remember('charge_request_' . $userTelegramId, 600, fn() => [
            'user' => $userTelegramId,
            'step_id' => $stepId,
            'charge_request' => [
                'payment_method_id' => $paymentMethod->id,
                'user_id' => $user->id
            ]
        ]);
    }
}
