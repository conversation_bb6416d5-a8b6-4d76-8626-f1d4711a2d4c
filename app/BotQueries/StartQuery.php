<?php

namespace App\BotQueries;

use App\BotQueries\Concerns\HandleQueryConcern;
use App\Facades\Bot;
use App\Facades\BotMessage;
use App\Facades\Settings;
use App\Services\UserService;
use Illuminate\Support\Facades\Log;

class StartQuery implements HandleQueryConcern
{

    public static function handle(array $params = []): void
    {
        $userService = app()->make(UserService::class);

        $tUser =  $params['from'];

        // Retrieve the user based on the telegram_id
        $user = $userService->where('telegram_id', $tUser['id'])->first();

        // Check if the user exists
        if (!$user) {
            $name = $tUser['first_name'];
            if (isset($tUser['last_name'])) {
                $name .= ' ' . $tUser['last_name'];
            }
            $userData = [
                'telegram_id' => $tUser['id'],
                'name' => $name
            ];
            $referral = Settings::get('referral_' . $tUser['id']);
            if ($referral) {
                $refUser = $userService->getByTelegramId($referral);
                $userData['user_id'] = $refUser?->id;
            }
            // Create a new user if not found
            $user = $userService->create($userData);
        }

        $greetingMessage = Settings::get('greeting_message');
        $message = BotMessage::setChat($tUser['id'])
            ->setText($greetingMessage ?? "أهلا وسهلا بك يرجى اختيار احد الخيارات التالية")
            ->addButton('⚡ ichancy', 'ichancy')
            ->addRow()
            ->addButton('   شحن رصيد في البوت 📥   ', 'charge')
            ->addButton('   سحب رصيد من البوت 📤   ', 'withdraw')
            ->addRow()
            ->addButton('  نظام الإحالات 💰 ', 'referral')
            ->addRow()
            ->addButton('إرسال رسالة للدعم 💭', 'support')
            ->addRow();
        if (isset($params['entities'][0]['type']) && $params['entities'][0]['type'] == 'bot_command') {
            Bot::sendMessage($message->toArray());
        } else {
            $message->setMessageId($params['message']['message_id']);
            Bot::editMessage($message->toArray());
        }
    }
}
