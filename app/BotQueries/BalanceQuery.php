<?php

namespace App\BotQueries;

use App\BotQueries\Concerns\HandleQueryConcern;
use App\Facades\Bot;
use App\Facades\BotMessage;
use App\Services\UserService;

class BalanceQuery implements HandleQueryConcern
{

    public static function handle(array $params = []): void
    {
        $userService = app()->make(UserService::class);

        $tUser =  $params['from'];

        $user = $userService->first([], ['telegram_id' => $tUser['id']]);

        if (!$user) return;
        $balance = $user->userBalance()->firstOrCreate();
        $text = 'رصيدك في البوت: ' . ($balance->active ?? 0) . ' ليرة';
        $message = BotMessage::setChat($tUser['id'])
            ->setText($text)
            ->toArray();

        Bot::sendMessage($message);
    }
}
