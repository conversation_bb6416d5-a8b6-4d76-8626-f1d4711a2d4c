<?php

namespace App\BotQueries;


use App\BotQueries\Concerns\HandleQueryConcern;
use App\Facades\Bot;
use App\Facades\BotMessage;
use Illuminate\Support\Facades\Cache;

class CancelWithdrawRequestQuery implements HandleQueryConcern
{
    public static function handle(array $params = []): void
    {
        $text = $params['message']['text'];
        $messageId = $params['message']['message_id'];
        Cache::forget('withdraw_request_' . $params['from']['id']);
        $text .= '
❌تم الغاء الطلب بنجاح';
        $message = BotMessage::setChat($params['from']['id'])
            ->setMessageId($messageId)
            ->setText($text)
            ->toArray();
        Bot::editMessage($message);
    }
}
