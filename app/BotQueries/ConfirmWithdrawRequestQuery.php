<?php

namespace App\BotQueries;

use App\BotQueries\Concerns\HandleQueryConcern;
use App\Enums\CommessionType;
use App\Facades\Bot;
use App\Facades\BotMessage;
use App\Facades\Settings;
use App\Services\UserService;
use App\Services\WithdrawMethodService;
use App\Services\WithdrawRequestService;
use Illuminate\Support\Facades\Cache;

class ConfirmWithdrawRequestQuery implements HandleQueryConcern
{
    public static function handle(array $params = []): void
    {

        $withdrawRequestService = app()->make(WithdrawRequestService::class);
        $userService = app()->make(UserService::class);
        $withdrawMethodService = app()->make(WithdrawMethodService::class);

        $text = $params['message']['text'];
        $messageId = $params['message']['message_id'];
        $userId = $params['from']['id'];
        $withdrawRequest = Cache::get('withdraw_request_' . $userId);
        if (!$withdrawRequest) return;
        $request = $withdrawRequest['withdraw_request'];
        $request['operation_id'] = $withdrawRequestService->generateRequestId();
        $user = $userService->getByTelegramId($userId);
        if (!$user) return;
        // Remove the withdraw request from the cache and create the withdraw request
        Cache::forget('withdraw_request_' . $userId);
        $createdRequest = $withdrawRequestService->create($request);
        $withdrawMethod = $withdrawMethodService->show($request['withdraw_method_id']);
        $commession = 0;
        if ($withdrawMethod->commession_type->value == CommessionType::PERCENT->value) {
            $commession = $request['amount'] * ($withdrawMethod->commession / 100);
        } else {
            $commession = $withdrawMethod->commession;
        }
        $text .= '
✅تم ارسال طلبك بنجاح يرجى الانتظار لتتم معالجة الطلب,رقم العملية: `' . $request['operation_id'] . '`';
        // Send a message to the user with the operation ID
        $message = BotMessage::setChat($userId)
            ->setMessageId($messageId)
            ->setText($text)
            ->toArray();
        Bot::editMessage($message);
        $adminGroup = Settings::get('admin_group_id');
        if (!$adminGroup) return;
        $message = BotMessage::setChat($adminGroup)
            ->setText('طلب سحب رصيد
الاسم: `' . $user->name . '`
معرف التلفرام: `' . $user->telegram_id . '`
اسم المستخدم: `' . $user->ichancyAccount->username . '`
معرف ايشانسي: `' . $user->ichancyAccount->player_id . '`
العمولة: `' . $commession . '`
المبلغ: `' . $request['amount'] - $commession . '`
طريقة السحب: `' . $createdRequest->withdrawMethod->name . '`
معلومات اضافية: `' . $request['payment_info'] . '`
رقم العملية: `' . $request['operation_id'] . '`')
            ->addButton('موافق ✅', 'confirm_withdraw_request_' . $createdRequest->id)
            ->addButton('رفض ❌', 'reject_withdraw_request_' . $createdRequest->id);
        Bot::sendMessage($message->toArray());
    }
}
