<?php

namespace App\BotQueries;

use App\BotQueries\Concerns\HandleQueryConcern;
use App\Facades\Bot;
use App\Facades\BotMessage;
use App\Services\UserService;
use Illuminate\Support\Facades\Cache;

class RegisterQuery implements HandleQueryConcern
{
    public static function handle(array $params = []): void
    {
        $userService = app()->make(UserService::class);
        $userId = $params['from']['id'];
        $user = $userService->getByTelegramId($userId);
        $message = BotMessage::setChat($userId)
            ->setMessageId($params['message']['message_id']);
        if ($user->ichancyAccount()->exists()) {
            $message->setText('لا يمكن انشاء أكثر من حساب');
            Bot::editMessage($message->toArray());
            return;
        }
        $message->setText('يرجى إدخال اسم المستخدم')
            ->toArray();
        Cache::remember('register_' . $userId, 600, fn() => [
            'data' => []
        ]);
        Bot::editMessage($message);
    }
}
