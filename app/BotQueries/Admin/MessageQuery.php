<?php

namespace App\BotQueries\Admin;

use App\BotQueries\Concerns\HandleQueryConcern;
use App\Facades\Settings;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class MessageQuery implements HandleQueryConcern
{
    public static function handle(array $params = []): void
    {
        $checkReply = Cache::get('admin_reply_support_message_' . $params['from']['id']);
        if ($checkReply) {
            ReplyToSupportMessageQuery::handle($params);
            return;
        }
        $group = Settings::get("admin_group_id");
        $checkBroadcast = Cache::get('broadcast_' . $group);
        if ($checkBroadcast) {
            Log::info('sending message to users');
            BroadcastQuery::handle($params);
            return;
        }
    }
}
