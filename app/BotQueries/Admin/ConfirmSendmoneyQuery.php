<?php

namespace App\BotQueries\Admin;

use App\BotQueries\Concerns\HandleQueryConcern;
use App\Facades\Bot;
use App\Facades\BotMessage;
use App\Services\UserService;

class ConfirmSendmoneyQuery implements HandleQueryConcern
{

    public static function handle(array $params = []): void
    {
        $data = explode('_', $params['data']);
        $userId = $data[count($data) - 1];
        $userService = app()->make(UserService::class);
        $user = $userService->show($userId);
        if (!$user) return;
        $messageObj = $params['message'];
        $group = $messageObj['chat']['id'];
        $groupMessage = BotMessage::setChat($group)
            ->setMessageId($messageObj['message_id'])
            ->setText('تم ارسال رسالة بتأكيد عملية التحويل للمستخدم *' . $user->name . '*');
        Bot::editMessage($groupMessage);
        $message = BotMessage::setChat($user->telegram_id)
            ->setText('تم تحويل المبلغ بنجاح ✅');
        Bot::sendMessage($message);
    }
}
