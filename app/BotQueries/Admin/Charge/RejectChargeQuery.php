<?php

namespace App\BotQueries\Admin\Charge;

use App\BotQueries\Concerns\HandleQueryConcern;
use App\Facades\Bot;
use App\Facades\BotMessage;
use App\Services\ChargeRequestService;

class RejectChargeQuery implements HandleQueryConcern
{
    public static function handle(array $params = []): void
    {
        $data = explode('_', $params['data']);
        $requestId = $data[count($data) - 1];
        $chargeRequestService = app()->make(ChargeRequestService::class);
        $messageObj = $params['message'];
        $group = $messageObj['chat']['id'];
        $groupMessage = BotMessage::setChat($group)
            ->setMessageId($messageObj['message_id']);
        $chargeRequest = $chargeRequestService->show($requestId, ['user.userBalance']);
        if (!$chargeRequest || $chargeRequest->status != 'pending') {
            $groupMessage->setText('لم يتم العثور على  الطلب');
            Bot::editMessage($groupMessage);
            return;
        }

        $user = $chargeRequest->user;
        $chargeRequest->update(['status' => 'rejected']);

        $groupMessage->setText('تم رفض الطلب بنجاح');
        Bot::editMessage($groupMessage->toArray());
        $chargeRequestService->sendStatusMessage($chargeRequest, $user->telegram_id);
    }
}
