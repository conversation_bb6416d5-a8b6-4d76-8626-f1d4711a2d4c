<?php

namespace App\BotQueries\Admin\Charge;

use App\BotQueries\Concerns\HandleQueryConcern;
use App\Facades\Bot;
use App\Facades\BotMessage;
use App\Services\ChargeRequestService;
use Illuminate\Support\Facades\Log;

class ConfirmChargeQuery implements HandleQueryConcern
{
    public static function handle(array $params = []): void
    {
        $data = explode('_', $params['data']);
        $requestId = $data[count($data) - 1];
        $chargeRequestService = app()->make(ChargeRequestService::class);

        $messageObj = $params['message'];
        $group = $messageObj['chat']['id'];
        $groupMessage = BotMessage::setChat($group)
            ->setMessageId($messageObj['message_id']);

        $chargeRequest = $chargeRequestService->show($requestId, ['user']);
        if (!$chargeRequest || $chargeRequest->status != 'pending') {
            $groupMessage->setText('لم يتم العثور على  الطلب');
            Bot::editMessage($groupMessage);
            return;
        }
        $user = $chargeRequest->user;

        $chargeRequest->update(['status' => 'approved', 'is_charged' => true]);
        $userBalance = $user->userBalance()->firstOrCreate();
        $userBalance->update(['active' => $userBalance->active + $chargeRequest->amount]);
        $groupMessage->setText('تمت الموافقة على الطلب بنجاح وإضافة الرصيد للمستخدم في البوت');
        Bot::editMessage($groupMessage->toArray());
        $chargeRequestService->sendStatusMessage($chargeRequest, $user->telegram_id);
    }
}
