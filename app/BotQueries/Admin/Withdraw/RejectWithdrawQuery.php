<?php

namespace App\BotQueries\Admin\Withdraw;

use App\BotQueries\Concerns\HandleQueryConcern;
use App\Facades\Bot;
use App\Facades\BotMessage;
use App\Services\WithdrawRequestService;
use Illuminate\Support\Facades\Cache;

class RejectWithdrawQuery implements HandleQueryConcern
{
    public static function handle(array $params = []): void
    {
        $data = explode('_', $params['data']);
        $requestId = $data[count($data) - 1];
        $withdrawRequestService = app()->make(WithdrawRequestService::class);
        $messageObj = $params['message'];
        $group = $messageObj['chat']['id'];
        $groupMessage = BotMessage::setChat($group)
            ->setMessageId($messageObj['message_id']);
        $withdrawRequest = $withdrawRequestService->show($requestId, ['user.userBalance']);
        if (!$withdrawRequest || $withdrawRequest->status != 'pending') {
            $groupMessage->setText('لم يتم العثور على  الطلب');
            Bot::editMessage($groupMessage);
            return;
        }

        $user = $withdrawRequest->user;
        $withdrawRequest->update(['status' => 'rejected']);

        $groupMessage->setText('تم رفض الطلب بنجاح');
        Bot::editMessage($groupMessage->toArray());
        $withdrawRequestService->sendStatusMessage($withdrawRequest, $user->telegram_id);
    }
}
