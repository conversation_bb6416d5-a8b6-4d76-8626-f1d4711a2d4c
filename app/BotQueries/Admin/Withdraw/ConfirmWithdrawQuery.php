<?php

namespace App\BotQueries\Admin\Withdraw;

use App\BotQueries\Concerns\HandleQueryConcern;
use App\Facades\Bot;
use App\Facades\BotMessage;
use App\Services\UserService;
use App\Services\WithdrawRequestService;
use Illuminate\Support\Facades\Log;

class ConfirmWithdrawQuery implements HandleQueryConcern
{
    public static function handle(array $params = []): void
    {
        $data = explode('_', $params['data']);
        $requestId = $data[count($data) - 1];
        $withdrawRequestService = app()->make(WithdrawRequestService::class);
        $messageObj = $params['message'];
        $group = $messageObj['chat']['id'];
        $groupMessage = BotMessage::setChat($group)
            ->setMessageId($messageObj['message_id']);
        $withdrawRequest = $withdrawRequestService->show($requestId, ['user.userBalance']);
        if (!$withdrawRequest || $withdrawRequest->status != 'pending') {
            $groupMessage->setText('لم يتم العثور على  الطلب');
            Bot::editMessage($groupMessage);
            return;
        }
        $user = $withdrawRequest->user;
        $withdrawRequest->update(['status' => 'approved']);
        $balnce = $user->userBalance->active - $withdrawRequest->amount;

        $user->userBalance()->update([
            'active' => $balnce,
        ]);
        $text  = $messageObj['text'] . '
*تمت الموافقة على الطلب* ✅';
        $groupMessage->setText($text)
            ->toArray();
        Bot::editMessage($groupMessage);
        $userMessage = BotMessage::setChat($user->telegram_id)
            ->setText('تمت الموافقة على طلب السحب, سيتم التحويل خلال مدة 72 ساعة')
            ->toArray();
        Bot::sendMessage($userMessage);
        $markup = BotMessage::setChat($group)
            ->setMessageId($messageObj['message_id'])
            ->addButton('إرسال تأكيد بعملية التحويل', 'confirm_sendmoney_' . $user->id);
        Bot::editMessageReplyMarkup($markup->toArray());
    }
}
