<?php

namespace App\BotQueries\Admin;

use App\BotQueries\Concerns\HandleQueryConcern;
use App\Facades\Bot;
use App\Facades\BotMessage;
use App\Facades\Settings;

class StartQuery implements HandleQueryConcern
{
    public static function handle(array $params = []): void
    {
        // $group = Settings::get("admin_group_id");
        // $message = BotMessage::setChat($group)
        //     ->setText('خيارات أدمن البوت')
        //     ->addButton('إرسال رسالة جماعية ✉️', 'broadcast');

        // Bot::sendMessage($message->toArray());
    }
}
