<?php

namespace App\BotQueries\Admin\Supprt;

use App\BotQueries\Concerns\HandleQueryConcern;
use App\Facades\Bot;
use App\Facades\BotMessage;
use App\Facades\Settings;
use Illuminate\Support\Facades\Cache;

class CancelReplyToMessageQuery implements HandleQueryConcern
{
    public static function handle(array $params = []): void
    {
        $check = Cache::get('admin_reply_support_message_' . $params['from']['id']);
        if (!$check) return;

        $group = Settings::get('support_group_id');
        $message = BotMessage::setChat($group)
            ->setMessageId($params['message']['message_id'])
            ->setText('تم الغاء الرد على المستخدم `' . $check['sender'] . '`');
        Bot::editMessage($message->toArray());
        Cache::forget('admin_reply_support_message_' . $params['from']['id']);
    }
}
