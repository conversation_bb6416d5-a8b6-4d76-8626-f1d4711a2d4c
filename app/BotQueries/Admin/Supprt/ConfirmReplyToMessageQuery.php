<?php

namespace App\BotQueries\Admin\Supprt;

use App\BotQueries\Concerns\HandleQueryConcern;
use App\Facades\Bot;
use App\Facades\BotMessage;
use App\Facades\Settings;
use Illuminate\Support\Facades\Cache;

class ConfirmReplyToMessageQuery implements HandleQueryConcern
{

    public static function handle(array $params = []): void
    {
        $check = Cache::get('admin_reply_support_message_' . $params['from']['id']);
        if (!$check) return;
        $group = Settings::get('support_group_id');
        $text = '**رسالة من الدعم**
```
' . $check['message'] . '
```

يرجى العلم أن هذه الرسالة لايمكن الرد عليها, لارسال رسالة أخرى يرجى الذهاب الى القائمة الرئيسية واختيار زر ارسال رسالة للدعم';
        $message = BotMessage::setChat($check['sender'])
            ->setText($text);
        Bot::sendMessage($message->toArray());
        Bot::editMessage(BotMessage::setChat($group)
            ->setMessageId($params['message']['message_id'])
            ->setText('تم إرسال رسالتك بنجاح للمستخدم `' . $check['sender'] . '`')
            ->toArray());
        Cache::forget('admin_reply_support_message_' . $params['from']['id']);
    }
}
