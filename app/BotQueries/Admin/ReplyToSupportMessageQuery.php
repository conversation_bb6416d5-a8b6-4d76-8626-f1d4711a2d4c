<?php

namespace App\BotQueries\Admin;

use App\BotQueries\Concerns\HandleQueryConcern;
use App\Facades\Bot;
use App\Facades\BotMessage;
use App\Facades\Settings;
use Illuminate\Support\Facades\Cache;

class ReplyToSupportMessageQuery implements HandleQueryConcern
{

    public static function handle(array $params = []): void
    {
        $check = Cache::get('admin_reply_support_message_' . $params['from']['id']);
        $group = Settings::get("support_group_id");
        if ($check && isset($params['text'])) {
            $message = BotMessage::setChat($group)
                ->setText('هل أنت متأكد بإرسال رسالتك للمستخدم `' . $check['sender'] . '`؟ 
"' . $params['text'] . '"')
                ->addButton('تأكيد 👍', 'confirm_admin_reply_support_message_' . $params['from']['id'])
                ->addButton('الغاء ❌', 'cancel_admin_reply_support_message_' . $params['from']['id']);
            Bot::sendMessage($message->toArray());
            Cache::forget('admin_reply_support_message_' . $params['from']['id']);
            Cache::remember('admin_reply_support_message_' . $params['from']['id'], 600, fn() => [
                'sender' => $check['sender'],
                'admin' => $check['admin'],
                'message' => $params['text']
            ]);
            return;
        }
        $message = BotMessage::setChat($group)
            ->setText('أدخل الرسالة التي ترغب بإرسالها 👇')
            ->toArray();
        Bot::sendMessage($message);
        $data = $params['data'];
        $parts = explode('_', $data);
        $sender = $parts[count($parts) - 1];
        if (!$sender) return;
        Cache::remember('admin_reply_support_message_' . $params['from']['id'], 600, fn() => [
            'sender' => $sender,
            'admin' => $params['from']['id']
        ]);
    }
}
