<?php

namespace App\BotQueries\Admin;

use App\BotQueries\Concerns\HandleQueryConcern;
use App\Facades\Bot;
use App\Facades\BotMessage;
use App\Facades\Settings;
use App\Models\User;
use App\Notifications\BroadcastMessage;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Notification;

class BroadcastQuery implements HandleQueryConcern
{
    public static function handle(array $params = []): void
    {
        $group = Settings::get("admin_group_id");
        $checkBroadcast = Cache::get('broadcast_' . $group);
        if ($checkBroadcast) {
            User::where('telegram_id', '!=', null)
                ->chunk(50, fn($chuk) => Notification::send($chuk, new BroadcastMessage($params['text'])));
            Cache::forget('broadcast_' . $group);
            $message = BotMessage::setChat($group)
                ->setText('تم ارسال الرسالة بنجاح');
            Bot::sendMessage($message->toArray());
            return;
        }
        Cache::forget('broadcast_' . $group);
        Cache::remember('broadcast_' . $group, 600, fn() => []);
        $message = BotMessage::setChat($group)
            ->setMessageId($params['message']['message_id'])
            ->setText('يرجى كتابة الرسالة المراد ارسالها');

        Bot::editMessage($message->toArray());
    }
}
