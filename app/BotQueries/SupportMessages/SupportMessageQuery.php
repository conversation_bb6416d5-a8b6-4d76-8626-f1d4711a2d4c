<?php

namespace App\BotQueries\SupportMessages;

use App\BotQueries\Concerns\HandleQueryConcern;
use App\Facades\Bot;
use App\Facades\BotMessage;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class SupportMessageQuery implements HandleQueryConcern
{

    public static function handle(array $params = []): void
    {
        $check = Cache::get('support_message_' . $params['from']['id']);
        if (!$check) {
            $message = BotMessage::setChat($params['from']['id'])
                ->setText('اكتب الرسالة التي ترغب بإرسالها ✉️');
            Bot::sendMessage($message->toArray());
            Cache::remember('support_message_' . $params['from']['id'], 600, fn() => ['message' => '']);
            return;
        }
        $message =  BotMessage::setChat($params['from']['id'])
            ->setText('هل أنت متأكد بأنك تريد ارسال الرسالة؟')
            ->addButton('تأكيد 👍', 'confirm_support_message_' . $params['from']['id'])
            ->addButton('الغاء ❌', 'cancel_support_message_' . $params['from']['id']);
        Bot::sendMessage($message->toArray());
        Cache::forget('support_message_' . $params['from']['id']);
        Cache::remember('support_message_' . $params['from']['id'], 600, fn() => ['message' => $params['text']]);
    }
}
