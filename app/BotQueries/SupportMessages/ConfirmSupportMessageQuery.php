<?php

namespace App\BotQueries\SupportMessages;

use App\BotQueries\Concerns\HandleQueryConcern;
use App\Facades\Bot;
use App\Facades\BotMessage;
use App\Facades\Settings;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class ConfirmSupportMessageQuery implements HandleQueryConcern
{

    public static function handle(array $params = []): void
    {
        Bot::editMessage(BotMessage::setChat($params['from']['id'])
            ->setMessageId($params['message']['message_id'])
            ->setText('تم إرسال رسالتك, سيتم الرد عليك في أسرع وقت ✅')
            ->toArray());
        $adminGroup = Settings::get('support_group_id');
        if (!$adminGroup) return;
        $supportMessage = Cache::get('support_message_' . $params['from']['id']);
        if (!$supportMessage || !isset($supportMessage['message']) || empty($supportMessage['message'])) return;

        $text = '📬 Telegram: @' .  $params['from']['username']  . '
🆔 User: ' . $params['from']['id'] . '
✉️ Message: ' . $supportMessage['message'];
        $message = BotMessage::setChat($adminGroup)
            ->setText($text)
            ->addButton('رد على الرسالة', 'reply_support_message_' . $params['from']['id'])
            ->setParseMode(null)
            ->toArray();
        $result = Bot::sendMessage($message);
        Cache::forget('support_message_' . $params['from']['id']);
    }
}
