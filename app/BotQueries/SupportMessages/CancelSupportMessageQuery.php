<?php

namespace App\BotQueries\SupportMessages;

use App\BotQueries\Concerns\HandleQueryConcern;
use App\Facades\Bot;
use App\Facades\BotMessage;
use Illuminate\Support\Facades\Cache;

class CancelSupportMessageQuery implements HandleQueryConcern
{
    public static function handle(array $params = []): void
    {
        Cache::forget('support_message_' . $params['from']['id']);
        Bot::editMessage(BotMessage::setChat($params['from']['id'])
            ->setMessageId($params['message']['message_id'])
            ->setText('تم إالغاء ارسال رسالتك للدعم')
            ->toArray());
    }
}
