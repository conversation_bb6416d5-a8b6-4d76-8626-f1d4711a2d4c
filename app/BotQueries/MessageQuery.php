<?php

namespace App\BotQueries;

use App\BotQueries\Admin\Withdraw\RejectWithdrawQuery;
use App\BotQueries\Concerns\HandleQueryConcern;
use App\BotQueries\Ichancy\ChargeIchancyQuery;
use App\BotQueries\Ichancy\WithdrawIchancyQuery;
use App\BotQueries\SupportMessages\SupportMessageQuery;
use App\Enums\CommessionType;
use App\Facades\Bot;
use App\Facades\BotMessage;
use App\Facades\Cashier;
use App\Facades\Settings;
use App\Models\IchancyAccount;
use App\Models\PaymentMessage;
use App\Models\User;
use App\Services\ChargeRequestService;
use App\Services\UserService;
use App\Services\WithdrawMethodService;
use App\Services\WithdrawRequestService;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class MessageQuery implements HandleQueryConcern
{
    public static function handle(array $params = []): void
    {
        $checkSupportMessage = Cache::get('support_message_' . $params['from']['id']);
        if ($checkSupportMessage) {
            SupportMessageQuery::handle($params);
            return;
        }
        $checkIchancyCharge = Cache::get('ichancy_charge_' . $params['from']['id']);
        if ($checkIchancyCharge) {
            ChargeIchancyQuery::handle($params);
            return;
        }
        $checkIchancyWithdraw = Cache::get('withdraw_ichancy_' . $params['from']['id']);
        if ($checkIchancyWithdraw) {
            WithdrawIchancyQuery::handle($params);
            return;
        }
        $userService = app()->make(UserService::class);

        $user = $userService->first(['ichancyAccount', 'userBalance'], ['telegram_id' => $params['from']['id']]);
        if (!$user) return;

        $chargeRequest = Cache::get('charge_request_' . $user->telegram_id);
        $withdrawRequest = null;
        if (!$chargeRequest) {
            $withdrawRequest = Cache::get('withdraw_request_' . $user->telegram_id);
        }
        $registerRequest = Cache::get('register_' . $user->telegram_id);

        if (!$chargeRequest && !$withdrawRequest && !$registerRequest) return;
        //handle charge request
        if ($chargeRequest) {
            static::charge($chargeRequest, $params, $user);
            return;
        }
        // handle withdraw request
        if ($withdrawRequest) {
            static::withdraw($withdrawRequest, $params, $user);
            return;
        }
        // handle register request
        if ($registerRequest) {
            static::register($registerRequest, $params, $user);
            return;
        }
    }
    /**
     * Handles the charging process.
     *
     * @param array $chargeRequest The charge request data.
     * @param array $params The parameters received from the user.
     * @param User $user The user object.
     * @return void
     */
    public static function charge($chargeRequest, $params, $user)
    {
        // Instantiate the ChargeRequestService
        $chargeRequestService = app()->make(ChargeRequestService::class);
        $userService = app()->make(UserService::class);
        $user = $userService->first(['ichancyAccount'], ['telegram_id' => $params['from']['id']]);
        // Retrieve the charge request data
        $request = $chargeRequest['charge_request'];

        // Check if the payment info is not set
        if (!isset($request['payment_info'])) {
            // Set the payment info and update the cache
            $request['payment_info'] = $params['text'];
            Cache::forget('charge_request_' . $user->telegram_id);
            Cache::remember('charge_request_' . $user->telegram_id, 600, fn() => [
                'user' => $user->telegram_id,
                'charge_request' => $request
            ]);

            // Send a message to the user asking for the transfer value
            $message = BotMessage::setChat($params['from']['id'])
                ->setText('يرجى ادخال قيمة التحويل')
                ->toArray();
            Bot::sendMessage($message);
            return;
        }

        // Check if the input is not numeric
        if (!is_numeric($params['text'])) {
            // Send a message to the user asking for a valid input
            $message = BotMessage::setChat($user->telegram_id)
                ->setText('يرجى إدخال رقم صحيح')
                ->toArray();
            Bot::sendMessage($message);
            return;
        }
        $minCharge = Settings::get('min_charge_amount');
        if ($minCharge) {
            if ($params['text'] < $minCharge) {
                $message = BotMessage::setChat($user->telegram_id)
                    ->setText('عذراً الحد الأدنى للشحن هو: ' . $minCharge)
                    ->toArray();
                Bot::sendMessage($message);
                return;
            }
        }
        // Set the amount and the operation ID
        $request['amount'] = $params['text'];
        $request['operation_id'] = $chargeRequestService->generateRequestId();


        // Remove the charge request from the cache and create the charge request
        Cache::forget('charge_request_' . $user->telegram_id);
        $createdRequest = $chargeRequestService->create($request);
        $checkPaymentMessage = PaymentMessage::where('payment_info', $request['payment_info'])
            ->where('payment_method_id', $request['payment_method_id'])
            ->where('amount', $request['amount'])
            ->first();
        if ($checkPaymentMessage) {
            $chargeRequestService->approve($createdRequest);
            $checkPaymentMessage->delete();
            return;
        }

        // Send a message to the user with the operation ID
        $message = BotMessage::setChat($params['from']['id'])
            ->setText('تم ارسال طلبك بنجاح يرجى الانتظار لتتم معالجة الطلب,رقم العملية: `' . $request['operation_id'] . '`')
            ->toArray();
        Bot::sendMessage($message);

        $adminGroup = Settings::get('admin_group_id');
        if (!$adminGroup) return;
        $message = BotMessage::setChat($adminGroup)
            ->setText('طلب شحن رصيد
الاسم: `' . $user->name . '`
معرف التلغرام: `' . $user->telegram_id . '`
اسم المستخدم: `' . ($user->ichancyAccount?->username ?? 'لا يوجد') . '`
معرف ايشانسي: `' . ($user->ichancyAccount?->player_id ?? 'لا يوجد') . '`
المبلغ: `' . $params['text'] . '`
طريقة الدفع: `' . $createdRequest->paymentMethod->name . '`
معلومات عملية الدفع: `' . $createdRequest->payment_info . '`
رقم العملية: `' . $createdRequest->operation_id . '`')
            ->addButton('موافق 👍', 'confirm_charge_request_' . $createdRequest->id)
            ->addButton('رفض ❌', 'reject_charge_request_' . $createdRequest->id);
        Bot::sendMessage($message->toArray());
    }
    /**
     * Handles the withdrawal process.
     *
     * @param array $withdrawRequest The withdraw request data.
     * @param array $params The parameters received from the user.
     * @param User $user The user object.
     * @return void
     */
    public static function withdraw($withdrawRequest, $params, $user)
    {
        // Instantiate the WithdrawRequestService
        $withdrawMethodService = app()->make(WithdrawMethodService::class);

        // Retrieve the withdraw request data
        $request = $withdrawRequest['withdraw_request'];

        // Check if the payment info is not set
        if (!isset($request['payment_info'])) {
            // Set the payment info and update the cache
            $request['payment_info'] = $params['text'];
            Cache::forget('withdraw_request_' . $user->telegram_id);
            Cache::remember('withdraw_request_' . $user->telegram_id, 600, fn() => [
                'user' => $user->telegram_id,
                'withdraw_request' => $request
            ]);

            // Send a message to the user asking for the withdrawal amount
            $message = BotMessage::setChat($user->telegram_id)
                ->setText('أدخل المبلغ المراد سحبه')
                ->toArray();
            Bot::sendMessage($message);
            return;
        }

        // Check if the input is not numeric
        if (!is_numeric($params['text'])) {
            // Send a message to the user asking for a valid input
            $message = BotMessage::setChat($user->telegram_id)
                ->setText('يرجى إدخال رقم صحيح')
                ->toArray();
            Bot::sendMessage($message);
            return;
        }
        $minWithdraw = Settings::get('min_withdraw_amount');
        if ($minWithdraw) {
            if ($params['text'] < $minWithdraw) {
                // Send a message to the user asking for a valid input
                $message = BotMessage::setChat($user->telegram_id)
                    ->setText('الحد الأدنى للسحب هو: ' . $minWithdraw)
                    ->toArray();
                Bot::sendMessage($message);
                return;
            }
        }
        // Check if the user has enough balance
        if (($user->userBalance?->active ?? 0) < floatval($params['text'])) {
            // Send a message to the user indicating insufficient balance
            $message = BotMessage::setChat($user->telegram_id)
                ->setText('ليس لديك رصيد كافي')
                ->toArray();
            Bot::sendMessage($message);
            return;
        }

        // Set the amount and the operation ID
        $request['amount'] = $params['text'];
        Cache::forget('withdraw_request_' . $user->telegram_id);
        Cache::remember('withdraw_request_' . $user->telegram_id, 600, fn() => [
            'user' => $user->telegram_id,
            'withdraw_request' => $request
        ]);
        $withdrawMethod = $withdrawMethodService->show($request['withdraw_method_id']);
        $commession = 0;
        if ($withdrawMethod->commession_type->value == CommessionType::PERCENT->value) {
            $commession = $request['amount'] * ($withdrawMethod->commession / 100);
        } else {
            $commession = $withdrawMethod->commession;
        }
        $message = BotMessage::setChat($user->telegram_id)
            ->setText('الرجاء التأكد من المعلومات قبل إرسال الطلب
طريقة السحب: ' . $withdrawMethod->name . '
المبلغ: ' . $request['amount'] . '
معلومات عملية السحب: ' . $request['payment_info'] . '
عمولة السحب: ' . $request['commession'] . '' . ($withdrawMethod->commession_type->value == CommessionType::PERCENT->value ? '%' : 'ليرة') . '
المبلغ المقتطع: ' . $commession . '
المبلغ الكلي بعد اقتطاع العمولة: ' . ($request['amount'] - $commession) . '')
            ->addButton('تأكيد ✅', 'confirm_withdraw_request_' . $user->telegram_id)
            ->addButton('إلغاء ❌', 'cancel_withdraw_request_' . $user->telegram_id);
        Bot::sendMessage($message->toArray());
    }

    public static function register($registerRequest, $params, $user)
    {
        $shouldRegister = false;
        $data = $registerRequest['data'];
        $message = BotMessage::setChat($user->telegram_id);
        if (!isset($data['login'])) {
            $data['login'] = $params['text'];
            $message = $message->setText('يرجى إدخال كلمة المرور');
            Bot::sendMessage($message->toArray());
        } elseif (!isset($data['password'])) {
            $data['password'] = $params['text'];
            $shouldRegister = true;
        }
        if (!$shouldRegister) {
            Cache::forget('register_' . $user->telegram_id);
            Cache::remember('register_' . $user->telegram_id, 600, fn() => [
                'data' => $data
            ]);
            return;
        }
        $statusMessage = Bot::sendMessage(BotMessage::setChat($user->telegram_id)->setText('جاري تسجيل حسابك, الرجاء الانتظار')->toArray());

        $usernames = IchancyAccount::where('username', 'like', $data['login'] . '%')->pluck('username');
        $username = $data['login'];
        do {
            $username .= rand(0, 9);
        } while ($usernames->contains($username));
        $data['login'] = $username;
        $data['email'] = $data['login'] . '@' . config('ichancy.cashier.name') . '.com';
        Cache::forget('register_' . $user->telegram_id);
        $password = $data['password'];
        if (!static::containsNumbers($password)) {
            $password .= rand(0, 999);
        }
        if (!static::containsSymbols($password)) {
            $password .= '@';
        }
        $data['password'] = $password;
        $response = Cashier::createUser($data, $user->telegram_id);
        if ($response === true) {
            return;
        }
        if (isset($response['result']) && $response['result'] == 1) {
            $user->ichancyAccount()->create([
                'username' => $data['login'],
                'password' => $data['password']
            ]);
            $text = "تم إنشاء الحساب بنجاح
*اسم المستخدم*: `" . $data['login'] . "`
*كلمة المرور*: `" . $data['password'] . "`";
            $message = $message
                ->setText($text);
            Bot::sendMessage($message->toArray());
            $player = Cashier::getPlayers(["players" => $data['login']]);
            if (isset($player['result']['records']) && !empty($player['result']['records'])) {
                $info = $player['result']['records'][0];
                $user->ichancyAccount()->update([
                    'player_id' => $info['playerId']
                ]);
            }
            return;
        }
        $message = $message->setText('عذراً حدث خطأ ما، يرجى المحاولة مرة أخرى');
        Bot::sendMessage($message->toArray());
    }
    public static function containsSymbols($password)
    {
        return preg_match('/[^a-zA-Z0-9]/', $password) > 0;
    }
    public static function containsNumbers($password)
    {
        return ctype_alnum($password);
    }
}
