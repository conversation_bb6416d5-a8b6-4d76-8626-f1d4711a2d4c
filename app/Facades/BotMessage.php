<?php

namespace App\Facades;

use Illuminate\Support\Facades\Facade;

/**
 * @method static $this setText(string $text)
 * @method static $this setChat(string $chatId)
 * @method static $this setMessageId(string $messageId)
 * @method static $this addRow()
 * @method static $this addButton(string $text, string | array $callbackData = [],?string $url = null)
 * @method static $this setParseMode(string $parseMode)
 * @method static array toArray()
 * @method static $this addEntity(array $entity)
 * @method static $this addQoute(string $text)
 * @method static $this setReplyParameters(array $reply_parametrs)
 * @see App\Helpers\BotMessage
 */
class BotMessage extends Facade
{
    protected static function getFacadeAccessor(): string
    {
        return 'bot_message';
    }
}
