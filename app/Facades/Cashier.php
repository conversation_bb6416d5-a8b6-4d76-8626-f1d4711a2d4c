<?php

namespace App\Facades;

use Illuminate\Support\Facades\Facade;

/**
 * @method static mixed getWallet()
 * @method static mixed createUser(array $data,string $userTelegramId)
 * @method static mixed getPlayers(array $filter = [])
 * @method static mixed depositToPlayer(string $playerId, string $amount)
 * @method static void depositToPlayerByUsername(string $amount, string $userTelegramId)
 * @method static void withdrawToPlayerByUsername(string $amount, string $userTelegramId)
 * @method static mixed withdrawFromPlayer(string $playerId, string $amount)
 * @method static array | null getPlayerDetails(string $username)
 * @method static int getPlayerBalanceById(string $playerId)
 * @method static mixed login()
 * @see \App\Helpers\Cashier
 */
class Cashier extends Facade
{

    protected static function getFacadeAccessor(): string
    {
        return 'Cashier';
    }
}
