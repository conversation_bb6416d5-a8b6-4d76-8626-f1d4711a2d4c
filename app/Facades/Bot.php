<?php

namespace App\Facades;

use Illuminate\Support\Facades\Facade;

/**
 * @method static $this setToken(string $token)
 * @method static void handleCommands()
 * @method static array editMessage(array $params)
 * @method static array sendMessage(array $params)
 * @method static array setBotName(string $name)
 * @method static array setBotDescription(string $name)
 * @method static array editMessageReplyMarkup(array | BotMessage $params)
 * @method static mixed getChatMember(string $channelId, string $userId)
 * @method static array getBotInfo()
 * @see App\Helpers\Bot
 */
class Bot extends Facade
{

    protected static function getFacadeAccessor(): string
    {
        return 'bot';
    }
}
