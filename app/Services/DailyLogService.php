<?php

namespace App\Services;

use App\Facades\Bot;
use App\Facades\BotMessage;
use App\Helpers\ChargeRequestStatus;
use App\Models\CashbackLog;
use App\Models\ChargeRequest;
use App\Models\Coupon;
use App\Models\DailyLog;
use App\Models\IchancyAccount;
use App\Models\ManualTransactionLog;
use App\Models\User;
use App\Models\UserBalance;
use App\Models\WheelLog;
use App\Models\WheelPrize;
use App\Models\WheelPrizeType;
use App\Models\WithdrawRequest;
use App\Services\Service;

class DailyLogService extends Service
{

    public function model()
    {
        return DailyLog::class;
    }
    public function getLogs($filters = [])
    {
        return $this->model::query()
            ->when(isset($filters['time']), fn($query) => $query->when($filters['time'] == 'today', fn($q) => $q->whereDate('created_at', now()))
                ->when($filters['time'] == 'this_week', fn($q) => $q->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()]))
                ->when($filters['time'] == 'yesterday', fn($q) => $q->whereDate('created_at', now()->yesterday())))
            ->first();
    }
    public function dailyLog($date = null)
    {
        if (!$date) {
            $date = now()->subDays(1);
        }
        $chargeRequests = ChargeRequest::query()
            ->where('status', ChargeRequestStatus::APPROVED)
            ->whereDate('created_at', $date)
            ->with('paymentMethod')
            ->selectRaw("SUM(amount) as total,COUNT(id) as count,payment_method_id")
            ->groupBy('payment_method_id')
            ->get();
        $withdrawRequests = WithdrawRequest::query()
            ->where('status', 'approved')
            ->whereDate('updated_at', $date)
            ->with('withdrawMethod')
            ->selectRaw("SUM(amount) as total,COUNT(id) as count,withdraw_method_id")
            ->groupBy('withdraw_method_id')
            ->get();
        $totalBalance = UserBalance::query()->selectRaw("SUM(active) as total")->value('total');
        $totalUsers = User::query()->selectRaw("COUNT(id) as count")
            ->whereDate('created_at', $date)
            ->value('count');
        $totalAccounts = IchancyAccount::query()->selectRaw("COUNT(id) as count")
            ->whereDate('created_at', $date)
            ->value('count');
        $totalManual = ManualTransactionLog::query()
            ->whereDate('created_at', $date)
            ->where('type', 'charge')
            ->selectRaw("SUM(amount) as total")
            ->value('total');
        $totalCodes = Coupon::query()
            ->whereDate('created_at', $date)
            ->selectRaw("COUNT(id) as count,SUM(value) as total")
            ->first();
        $totalComession = WithdrawRequest::query()
            ->selectRaw("SUM(commession) as total")
            ->where('status', 'approved')
            ->whereDate('updated_at', $date)
            ->value('total');

        // Get total cashback amount for the day
        $totalCashback = CashbackLog::query()
            ->whereDate('created_at', $date)
            ->selectRaw("SUM(cashback) as total")
            ->value('total') ?? 0;

        // Get cashback breakdown by payment method
        $cashbackByMethod = CashbackLog::query()
            ->whereDate('created_at', $date)
            ->with('paymentMethod')
            ->selectRaw("SUM(cashback) as total, COUNT(id) as count, payment_method_id")
            ->groupBy('payment_method_id')
            ->get();

        // Get total wheel prizes value where prize type slug is 'money'
        $moneyPrizeType = WheelPrizeType::where('slug', 'money')->first();
        $totalWheelPrizes = 0;
        $wheelPrizesCount = 0;

        if ($moneyPrizeType) {
            $wheelPrizesData = WheelLog::query()
                ->whereDate('wheel_logs.created_at', $date)
                ->whereHas('wheelPrize', function ($query) use ($moneyPrizeType) {
                    $query->where('wheel_prize_type_id', $moneyPrizeType->id);
                })
                ->join('wheel_prizes', 'wheel_logs.wheel_prize_id', '=', 'wheel_prizes.id')
                ->selectRaw('SUM(wheel_prizes.value) as total, COUNT(wheel_logs.id) as count')
                ->first();

            if ($wheelPrizesData) {
                $totalWheelPrizes = $wheelPrizesData->total ?? 0;
                $wheelPrizesCount = $wheelPrizesData->count ?? 0;
            }
        }
        $totalCharge = 0;
        $totalWithdraw = 0;
        $chargeCount = 0;
        $withdrawCount = 0;
        $text = 'إحصائيات تاريخ: ' . $date->toDateString() . '
عمليات الشحن
        ';
        foreach ($chargeRequests as $chargeRequest) {
            $text .= "
" . $chargeRequest->paymentMethod->name . "
عدد عمليات الشحن: $chargeRequest->count
مجموع المبالغ: " . number_format($chargeRequest->total) . " SYP
---------------------
            ";
            $totalCharge += $chargeRequest->total;
            $chargeCount += $chargeRequest->count;
        }
        $text .= "
<b>مجموع كل عمليات الشحن: " . number_format($totalCharge) . " SYP</b>
=======================
عمليات السحب
";
        foreach ($withdrawRequests as $withdrawRequest) {
            $text .= "
" . $withdrawRequest->withdrawMethod->name . "
عدد عمليات السحب: $withdrawRequest->count
مجموع المبالغ: " . number_format($withdrawRequest->total) . " SYP
---------------------
            ";
            $totalWithdraw += $withdrawRequest->total;
            $withdrawCount += $withdrawRequest->count;
        }
        $text .= "
<b>مجموع كل عمليات السحب: " . number_format($totalWithdraw) . " SYP</b>
=======================
عمليات الكاش باك
";
        // Add cashback breakdown by payment method
        $cashbackCount = 0;
        foreach ($cashbackByMethod as $cashback) {
            $methodName = $cashback->paymentMethod ? $cashback->paymentMethod->name : 'غير محدد';
            $text .= "
" . $methodName . "
عدد عمليات الكاش باك: $cashback->count
مجموع المبالغ: " . number_format($cashback->total) . " SYP
---------------------
            ";
            $cashbackCount += $cashback->count;
        }

        $text .= "
<b>مجموع كل عمليات الكاش باك: " . number_format($totalCashback) . " SYP</b>
=======================

";


        $text .= "
رصيد المستخدمين: " . number_format($totalBalance) . " SYP
عدد الحسابات الجديدة: $totalAccounts
عدد المستخدمين الجدد: $totalUsers
مجموع عمليات الشحن اليدوية: " . number_format($totalManual) . " SYP
عدد الأكواد: $totalCodes->count
مجموع الاكواد المستخدمة: " . number_format($totalCodes->total) . " SYP
=======================
عدد عمليات الشحن: $chargeCount
عدد عمليات السحب: $withdrawCount
عدد عمليات الكاش باك: $cashbackCount
عدد جوائز العجلة النقدية: $wheelPrizesCount
إجمالي العمولة: " . number_format($totalComession) . " SYP
إجمالي الكاش باك: " . number_format($totalCashback) . " SYP
إجمالي جوائز العجلة النقدية: " . number_format($totalWheelPrizes) . " SYP
";

        $group = config('ichancy.groups.reports_group');
        if (!$group)
            return;
        return Bot::sendMessage(
            BotMessage::setChat($group)
                ->setParseMode('HTML')
                ->setText($text)
                ->toArray()
        );
    }
}
