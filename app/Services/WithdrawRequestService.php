<?php

namespace App\Services;

use App\Facades\Bot;
use App\Facades\BotMessage;
use App\Models\WithdrawRequest;
use App\Services\Service;

class WithdrawRequestService extends Service
{

    public function model()
    {
        return WithdrawRequest::class;
    }
    public function sendStatusMessage(WithdrawRequest $request, string  $telegram_id)
    {
        $status = match ($request->status) {
            'approved' => 'تمت الموافقة على طلب السحب, رقم العملية: `' . $request->operation_id . '`  ',
            'rejected' => 'تم رفض طلب السحب,رقم العملية: `' . $request->operation_id . '`  ',
            default => null
        };
        if (!$status) return;
        if ($request->status == 'rejected' && $request->message) {
            $status .= '
سبب الرفض:
' . $request->message;
        }
        $message = BotMessage::setChat($telegram_id)
            ->setText($status)
            ->toArray();
        Bot::sendMessage($message);
    }
    public function generateRequestId()
    {
        $id = date('Ymd') . rand(0, 9999);
        $requests = $this->model::query()->where('operation_id', 'like', date('Ymd') . '%')
            ->pluck('operation_id');
        do {
            $id = date('Ymd') . rand(0, 9999);
        } while ($requests->contains($id));
        return $id;
    }
    public function getTotalWithdraw($filters = [])
    {
        return $this->model::query()
            ->when(isset($filters['time']), fn($query) => $query->when($filters['time'] == 'today', fn($q) => $q->whereDate('updated_at', now()))
                ->when($filters['time'] == 'this_week', fn($q) => $q->whereBetween('updated_at', [now()->startOfWeek(), now()->endOfWeek()]))
                ->when($filters['time'] == 'yesterday', fn($q) => $q->whereDate('updated_at', now()->yesterday()))
                ->when($filters['time'] == 'this_month', fn($q) => $q->whereMonth('updated_at', now()->month)->whereYear('updated_at', now()->year)))
            ->where('status', 'approved')->sum('amount');
    }
}
