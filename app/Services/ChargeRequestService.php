<?php

namespace App\Services;

use App\Facades\Bot;
use App\Facades\BotMessage;
use App\Models\CashbackLog;
use App\Models\ChargeRequest;
use App\Models\User;
use App\Services\Service;

class ChargeRequestService extends Service
{

    public function model()
    {
        return ChargeRequest::class;
    }

    public function sendStatusMessage(ChargeRequest $request, string $telegram_id)
    {
        $status = match ($request->status) {
            'approved' => 'تمت الموافقة على طلب الشحن, رقم العملية: `' . $request->operation_id . '` وإضافة المبلغ الى رصيدك في البوت',
            'rejected' => 'تم رفض طلب الشحن,رقم العملية: `' . $request->operation_id . '`  ',
            default => null
        };
        if (!$status)
            return;
        if ($request->status == 'rejected' && $request->message) {
            $status .= '
سبب الرفض:
' . $request->message;
        }
        $message = BotMessage::setChat($telegram_id)
            ->setText($status)
            ->toArray();
        Bot::sendMessage($message);
    }
    public function generateRequestId()
    {
        $id = date('Ymd') . rand(0, 9999);
        $requests = $this->model::query()->where('operation_id', 'like', date('Ymd') . '%')
            ->pluck('operation_id');
        do {
            $id = date('Ymd') . rand(0, 9999);
        } while ($requests->contains($id));
        return $id;
    }
    public function getTotalChargeAmount($filters = [])
    {

        return $this->model::query()
            ->when(isset($filters['time']), fn($query) => $query->when($filters['time'] == 'today', fn($q) => $q->whereDate('created_at', now()))
                ->when($filters['time'] == 'this_week', fn($q) => $q->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()]))
                ->when($filters['time'] == 'yesterday', fn($q) => $q->whereDate('created_at', now()->yesterday()))
                ->when($filters['time'] == 'this_month', fn($q) => $q->whereMonth('created_at', now()->month)->whereYear('created_at', now()->year))
                ->when($filters['time'] == 'last_month', fn($q) => $q->whereMonth('created_at', now()->month - 1)->whereYear('created_at', now()->year))
                ->when($filters['time'] == 'last_week', fn($q) => $q->whereBetween('created_at', [now()->subWeek()->startOfWeek(), now()->subWeek()->endOfWeek()])))
            ->where('status', 'approved')->sum('amount');
    }
    public function approve(ChargeRequest $request)
    {
        $request->status = 'approved';
        $request->save();
        if (!$request->is_charged) {
            $user = User::find($request->user_id);
            $balance = $user->userBalance()->firstOrCreate();
            $chargeAmount = $request->amount;
            $balance->active = ($balance->active ?? 0) + $chargeAmount;
            $balance->save();

            // Check if payment method has cashback enabled
            $paymentMethod = $request->paymentMethod;
            if ($paymentMethod && $paymentMethod->enable_cashback && $paymentMethod->cashback_percent > 0) {
                // Calculate cashback amount
                $cashbackAmount = ($chargeAmount * $paymentMethod->cashback_percent) / 100;

                if ($cashbackAmount > 0) {
                    // Add cashback to user balance
                    $balance->active += $cashbackAmount;
                    $balance->save();

                    // Create cashback log
                    CashbackLog::create([
                        'user_id' => $user->id,
                        'payment_method_id' => $paymentMethod->id,
                        'balance' => $chargeAmount,
                        'cashback' => $cashbackAmount,
                        'type' => 'percent',
                    ]);

                    // Notify user about cashback
                    $cashbackMessage = "تم إضافة كاش باك بقيمة $cashbackAmount ليرة إلى رصيدك بنسبة {$paymentMethod->cashback_percent}% من قيمة الشحن";
                    $message = BotMessage::setChat($user->telegram_id)
                        ->setText($cashbackMessage)
                        ->toArray();
                    Bot::sendMessage($message);
                }
            }

            $request->is_charged = true;
            $request->save();
        }
        $this->sendStatusMessage($request, $request->user->telegram_id);
    }
}
