<?php

namespace App\Services;

use App\Models\Coupon;
use App\Services\Service;

class CouponService extends Service
{

    public function model()
    {
        return Coupon::class;
    }
    public function generate($number, $value)
    {
        for ($i = 0; $i < $number; $i++) {
            $this->model::create([
                'code' => $this->generateCode(),
                'value' => $value,
                'is_active' => true,
            ]);
        }
    }
    public function generateCode()
    {
        $codes = $this->model::query()->where('code', 'like', date('Ymd') . '%')->pluck('code');
        do {
            $code = date('Ymd') . rand(1000, 9999);
        } while ($codes->contains($code));
        return $code;
    }
}
