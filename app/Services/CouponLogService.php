<?php

namespace App\Services;

use App\Models\CouponLog;
use App\Services\Service;

class CouponLogService extends Service
{

    public function model()
    {
        return CouponLog::class;
    }
    public function getLogs($filters = [])
    {
        return $this->model::query()
            ->when(
                isset($filters['time']),
                fn($query) => $query
                    ->when($filters['time'] == 'today', fn($q) => $q->whereDate('coupon_logs.created_at', now()))
                    ->when($filters['time'] == 'yesterday', fn($q) => $q->whereDate('coupon_logs.created_at', date('Y-m-d', strtotime("-1 days"))))
                    ->when($filters['time'] == 'this_week', fn($q) => $q->whereBetween('coupon_logs.created_at', [now()->startOfWeek(), now()->endOfWeek()]))
                    ->when($filters['time'] == 'this_month', fn($q) => $q->whereMonth('coupon_logs.created_at', now()->month)->whereYear('coupon_logs.created_at', now()->year))
            )
            ->join('coupons', 'coupon_logs.coupon_id', '=', 'coupons.id')
            ->sum('coupons.value');
    }
}
