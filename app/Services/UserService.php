<?php

namespace App\Services;

use App\Models\User;
use App\Services\Service;

class UserService extends Service
{

    public function model()
    {
        return User::class;
    }
    public function getByTelegramId($telegram_id)
    {
        return $this->model::query()
            ->with(['userBalance', 'ichancyAccount'])
            ->where('is_active', true)
            ->where('telegram_id', $telegram_id)
            ->first();
    }
    public function getTotalUsers()
    {
        return $this->model::query()
            ->where('email', null)
            ->where('is_active', true)
            ->count();
    }
}
