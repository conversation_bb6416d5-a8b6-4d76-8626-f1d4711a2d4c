<?php

namespace App\Services;

use App\Facades\Bot;
use App\Facades\BotMessage;
use App\Facades\Settings;
use App\Models\ChargeRequest;
use App\Models\ReferralCalculation;
use App\Models\User;
use App\Models\WithdrawRequest;
use App\Services\Service;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Number;

class ReferralCalculationService extends Service
{

    public function model()
    {
        return ReferralCalculation::class;
    }
    public function calculate()
    {
        DB::transaction(function () {


            $totalReferrals = 0;
            $totalActive = 0;
            $totalProfit = 0;
            $lastRefCalc = $this->model::latest()->first();
            $calcPeriod = Settings::get('referral_calculation') ?? 10;
            if ($lastRefCalc && $lastRefCalc->created_at->addDays(intval($calcPeriod)) > now()) {
                return;
            }
            $calculation = $this->model::create([
                'total_referrals' => 0,
                'total_active_referrals' => 0,
                'total_profit' => 0
            ]);
            $refPercent = Settings::get('referral_percent') ?? 0;
            $profitType = Settings::get('profit_type') ?? 'on_burn';
            $text = '';
            User::query()
                ->whereHas('referrals')
                ->with('referrals.ichancyAccount')
                ->chunk(50, function ($users) use (&$text, &$totalReferrals, &$totalActive, &$totalProfit, &$calculation, $refPercent, $profitType, $lastRefCalc) {
                    if ($profitType == 'on_ref') {
                        $res = $this->calculateOnRef($users, $text, $totalReferrals, $totalActive, $totalProfit, $calculation, $refPercent, $lastRefCalc);
                    } else {
                        $res = $this->calculateOnBurnOrCharge($users, $text, $totalReferrals, $totalActive, $totalProfit, $calculation, $refPercent, $profitType, $lastRefCalc);
                    }

                    $totalReferrals = $res['total_referrals'];
                    $totalActive = $res['total_active_referrals'];
                    $totalProfit = $res['total_profit'];
                    $text .= $res['text'];
                });
            $calculation->update([
                'total_referrals' => $totalReferrals,
                'total_active_referrals' => $totalActive,
                'total_profit' => $totalProfit
            ]);
            //$this->sendReport($totalReferrals, $totalActive, $totalProfit, $text);
        });
    }

    public function calculateOnRef($users, $text, $totalReferrals, $totalActive, $totalProfit, $calculation, $refPercent, $lastRefCalc)
    {
        $minRefCharge = Settings::get('min_ref_charge') ?? 0;
        $refs = User::whereHas('ichancyLogs', fn($query) => $query->where('amount', '>=', $minRefCharge))
            ->whereHas('ichancyAccount', fn($q) => $q
                ->when($lastRefCalc?->created_at, fn($q) => $q->whereDate('created_at', '>', $lastRefCalc->created_at)))
            ->whereIn('user_id', $users->pluck('id')->toArray())
            ->get();
        $totalRefs = User::whereHas('referrals')
            ->withCount('referrals')
            ->whereIn('user_id', $users->pluck('id')->toArray())
            ->get();
        $totalActive = $refs->count();
        $totalReferrals = $totalRefs->count();
        $totalProfit = $totalActive * $refPercent;
        $items = [];
        foreach ($users as $user) {
            $totalUserRefs = $totalRefs->where('user_id', $user->id)->count();
            $count = $refs->where('user_id', $user->id)->count();
            $userProfit = $count * $refPercent;
            if ($userProfit > 0) {

                $text .= '
<blockquote>معرف المستخدم: <code>' . $user->telegram_id . '</code> 

إجمالي الإحالات: ' . $totalUserRefs . '

إجمالي الإحالات النشطة: ' . $count . '

الربح: ' . Number::format($userProfit) . '
</blockquote>
                ';
                $items[] = [
                    'total_referrals' => $totalUserRefs,
                    'total_active_referrals' => $count,
                    'total_profit' => $userProfit,
                    'user_id' => $user->id
                ];
            }
            $calculation->items()->createMany($items);
        }
        return
            [
                'total_referrals' => $totalReferrals,
                'total_active_referrals' => $totalActive,
                'total_profit' => $totalProfit,
                'text' => $text
            ];
    }
    public function calculateOnBurnOrCharge($users, $text, $totalReferrals, $totalActive, $totalProfit, $calculation, $refPercent, $profitType, $lastRefCalc)
    {
        $items = [];
        $withdrawRequests = WithdrawRequest::query()->where('status', 'approved')
            ->when($lastRefCalc?->created_at, fn($q) => $q->whereDate('created_at', '>', $lastRefCalc->created_at))
            ->get();
        $comessionType = Settings::get('comession_type') ?? 'percent';
        $chargeRequests = ChargeRequest::query()
            ->when($lastRefCalc?->created_at, fn($q) => $q->whereDate('created_at', '>', $lastRefCalc->created_at))
            ->where('status', 'approved')
            ->get();
        foreach ($users as $user) {
            $totalUserReferrals = $user->referrals()->count();
            $totalUserActiveRefs = $user->referrals()->whereHas('ichancyAccount')->count();
            $totalUserProfit = 0;
            if ($user->ref_percent > 0) {
                $totalCharge = $chargeRequests->whereIn('user_id', $user->referrals->pluck('id')->toArray())->sum('amount');
                if ($profitType == 'on_burn') {
                    $totalWithdraw = $withdrawRequests->whereIn('user_id', $user->referrals->pluck('id')->toArray())->sum('amount');
                    $amount = $totalCharge - $totalWithdraw;
                    if ($amount > 0) {
                        if ($comessionType == 'percent') {
                            $totalUserProfit = $amount * $user->ref_percent / 100;
                        } else {
                            $totalUserProfit = $totalUserActiveRefs * $user->ref_percent;
                        }
                    }
                } else {
                    if ($comessionType == 'percent') {

                        $totalUserProfit = $totalCharge * $refPercent / 100;
                    } else {

                        $totalUserProfit = $totalUserActiveRefs * $refPercent;
                    }
                }
            }
            $itemData = [
                'total_referrals' => $totalUserReferrals,
                'total_active_referrals' => $totalUserActiveRefs,
                'total_profit' => $totalUserProfit,
                'user_id' => $user->id
            ];
            if ($totalUserProfit > 0) {

                $text .= '
معرف المستخدم: <code>' . $user->telegram_id . '</code> 
<a href="tg://user?id=' . $user->telegram_id . '">رابط الحساب</a>

إجمالي الإحالات: ' . $totalUserReferrals . '
نسبة المستخدم: ' . $user->ref_percent . '%
إجمالي الإحالات النشطة: ' . $totalUserActiveRefs . '

الربح: ' . Number::format($totalUserProfit) . '
------------------------
                ';
                $totalReferrals += $totalUserReferrals;
                $totalActive += $totalUserActiveRefs;
                $totalProfit += $totalUserProfit;
                $items[] = $itemData;
            }
        }
        $calculation->items()->createMany($items);
        return [
            'total_referrals' => $totalReferrals,
            'total_active_referrals' => $totalActive,
            'total_profit' => $totalProfit,
            'text' => $text
        ];
    }
    public function sendReport($totalReferrals, $totalActive, $totalProfit, $text)
    {

        $group = Settings::get('send_logs_to');
        $token = Settings::get('telegram_token');

        if ($group && $token) {
            $text = 'تقرير الإحالات لليوم ' . now()->format('Y-m-d') . '

إجمالي الإحالات: ' . $totalReferrals . '
إجمالي الإحالات النشطة: ' . $totalActive . '
إجمالي الربح: ' . Number::format($totalProfit) . '
============================
' . $text;
            $message = BotMessage::setChat($group)
                ->setParseMode('HTML')
                ->setText($text)
                ->toArray();
            // Bot::setToken($token)->sendMessage($message);
        }
    }
}
