<?php

namespace App\Services;

use App\Models\Setting;
use App\Services\Service;
use Illuminate\Support\Facades\Cache;

class SettingService extends Service
{

    public function model()
    {
        return Setting::class;
    }

    public function getToken()
    {
        return $this->model::where('key', 'telegram_token')->value('value');
    }
    public function get($key)
    {
        $value = Cache::get($key);
        if ($value === null) {
            $value = $this->model::where('key', $key)->value('value');
            Cache::forever($key, $value);
        }
        return $value;
    }
    public function set($key, $value)
    {
        Cache::forget($key);
        Cache::forever($key, $value);
        return $this->model::query()->updateOrCreate(['key' => $key], ['value' => $value]);
    }
}
