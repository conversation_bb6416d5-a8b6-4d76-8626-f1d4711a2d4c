<?php

namespace App\Exports;

use App\Models\WheelPrize;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class WheelPrizesExport implements FromCollection, WithHeadings, WithMapping, WithStyles
{
    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return WheelPrize::with('wheelPrizeType')->get();
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'ID',
            'Name',
            'Value',
            'Prize Type',
            'Prize Type Slug',
            'Win Percent',
            'Sort Order',
            'Is Active',
            'Created At',
            'Updated At',
        ];
    }

    /**
     * @param WheelPrize $wheelPrize
     * @return array
     */
    public function map($wheelPrize): array
    {
        return [
            $wheelPrize->id,
            $wheelPrize->name,
            $wheelPrize->value,
            $wheelPrize->wheelPrizeType ? $wheelPrize->wheelPrizeType->name : '',
            $wheelPrize->wheelPrizeType ? $wheelPrize->wheelPrizeType->slug : '',
            $wheelPrize->win_percent,
            $wheelPrize->sort,
            $wheelPrize->is_active ? 'Yes' : 'No',
            $wheelPrize->created_at ? $wheelPrize->created_at->format('Y-m-d H:i:s') : '',
            $wheelPrize->updated_at ? $wheelPrize->updated_at->format('Y-m-d H:i:s') : '',
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold text.
            1 => ['font' => ['bold' => true]],
        ];
    }
}
