<?php

namespace App\Exports;

use App\Models\WheelPrize;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;

class WheelPrizesTemplateExport implements FromCollection
{
    /**
     * @return array
     */
    public function collection(): Collection
    {
        // Get wheel prize types from database to create realistic examples
        return WheelPrize::select('wheel_prize_type_id', 'value', 'win_percent', 'is_active', 'sort')->get();
    }

}
