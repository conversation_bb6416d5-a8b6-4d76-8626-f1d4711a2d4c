<?php

namespace App\BotCommands;

use App\BotQueries\Admin\StartQuery as AdminStartQuery;
use App\BotQueries\StartQuery;
use App\Facades\BotMessage;
use App\Models\Setting;
use App\Services\UserService;
use Illuminate\Support\Facades\Log;

class StartCommand extends Command
{


    public function handle()
    {
        if (in_array($this->message['chat']['type'], ['group', 'supergroup'])) {
            AdminStartQuery::handle($this->message);
        } else {
            StartQuery::handle($this->message);
        }
    }
}
