<?php

namespace App\BotCommands;

use App\Helpers\Bot;

abstract class Command
{

    protected $message;
    protected Bot $bot;
    protected $update;
    public function __construct(Bot $bot, $message = null)
    {
        $this->bot = $bot;
        $this->message = $message;
        $this->update = collect($message);
    }
    public abstract function handle();

    /**
     * Sends a message to the user.
     *
     * @param array $params The parameters for the message.
     * @return void
     */
    public function replyWithMessage(array $params)
    {
        // Create a new array to hold the message data.
        $data = $params;

        // Add the chat ID to the message data.
        $data['chat_id'] = $this->update['message']['chat']['id'];

        // Use the bot to send the message to the user.
        $this->bot->sendMessage($data);
    }
}
