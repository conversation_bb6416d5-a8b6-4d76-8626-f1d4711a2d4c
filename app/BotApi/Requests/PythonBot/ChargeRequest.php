<?php

namespace App\BotApi\Requests\PythonBot;

use Saloon\Contracts\Body\HasBody;
use Saloon\Enums\Method;
use Saloon\Http\Request;
use Saloon\Traits\Body\HasJsonBody;

class ChargeRequest extends Request implements HasBody
{
    use HasJsonBody;

    protected Method $method = Method::POST;

    public function __construct(protected array $data) {}
    public function resolveEndpoint(): string
    {
        return 'charge';
    }
    protected function defaultBody(): array
    {
        return [
            ...$this->data,
            ...[
                'cashier_username' => config('ichancy.cashier.username'),
                'cashier_password' => config('ichancy.cashier.password')
            ]
        ];
    }
}
