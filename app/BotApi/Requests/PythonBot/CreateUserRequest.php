<?php

namespace App\BotApi\Requests\PythonBot;

use Saloon\Contracts\Body\HasBody;
use <PERSON>oon\Enums\Method;
use Saloon\Http\Request;
use Saloon\Traits\Body\HasJsonBody;

class CreateUserRequest extends Request implements HasBody
{
    use HasJsonBody;

    protected Method $method = Method::POST;

    protected $username;
    protected $password;

    public function __construct(protected array $data)
    {
        $this->username = config('ichancy.cashier.username');
        $this->password = config('ichancy.cashier.password');
    }
    public function resolveEndpoint(): string
    {
        return 'create-user';
    }
    protected function defaultBody(): array
    {
        return [
            ...[
                'cashier_username' => $this->username,
                'cashier_password' => $this->password
            ],
            ...$this->data
        ];
    }
}
