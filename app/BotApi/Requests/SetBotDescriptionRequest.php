<?php

namespace App\BotApi\Requests;

use Saloon\Contracts\Body\HasBody;
use Saloon\Enums\Method;
use Saloon\Http\Request;
use Saloon\Traits\Body\HasJsonBody;

class SetBotDescriptionRequest extends Request implements HasBody
{
    use HasJsonBody;
    protected Method $method = Method::POST;

    public function __construct(protected string $description) {}
    public function resolveEndpoint(): string
    {
        return 'setMyDescription';
    }

    protected function defaultBody(): array
    {
        return [
            'description' => $this->description
        ];
    }
}
