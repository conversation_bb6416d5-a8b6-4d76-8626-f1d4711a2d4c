<?php

namespace App\BotApi\Requests;

use Saloon\Contracts\Body\HasBody;
use Saloon\Enums\Method;
use Saloon\Http\Request;
use Saloon\Traits\Body\HasJsonBody;

class GetPlayerBalanceRequest extends Request implements HasBody
{
    use HasJsonBody;
    protected Method $method = Method::GET;

    public function __construct(protected string $playerId) {}
    public function resolveEndpoint(): string
    {
        return config('ichancy.endpoints.get_player_balance_by_id');
    }
    protected function defaultBody(): array
    {
        return ['playerId' => $this->playerId];
    }
}
