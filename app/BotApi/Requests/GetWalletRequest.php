<?php

namespace App\BotApi\Requests;

use Saloon\Contracts\Body\HasBody;
use Saloon\Enums\Method;
use Saloon\Http\Request;
use Saloon\Traits\Body\HasJsonBody;

class GetWalletRequest extends Request implements HasBody
{
    use HasJsonBody;
    protected Method $method = Method::POST;

    public function resolveEndpoint(): string
    {
        return config('ichancy.endpoints.wallet');
    }

    protected function defaultBody(): array
    {
        return [];
    }
}
