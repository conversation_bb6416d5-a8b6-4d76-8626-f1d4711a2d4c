<?php

namespace App\BotApi\Requests;

use Saloon\Contracts\Body\HasBody;
use Saloon\Enums\Method;
use Saloon\Http\Request;
use Saloon\Traits\Body\HasJsonBody;

class SendMessageRequest extends Request implements HasBody
{
    use HasJsonBody;

    protected Method $method = Method::POST;

    public function __construct(protected array $params) {}
    public function resolveEndpoint(): string
    {
        return 'sendMessage';
    }
    protected function defaultBody(): array
    {
        return $this->params;
    }
}
