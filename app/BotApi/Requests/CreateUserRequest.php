<?php

namespace App\BotApi\Requests;

use Saloon\Contracts\Body\HasBody;
use Saloon\Enums\Method;
use Saloon\Http\Request;
use Saloon\Traits\Body\HasJsonBody;

class CreateUserRequest extends Request implements HasBody
{
    use HasJsonBody;
    protected Method $method = Method::POST;

    public function __construct(protected array $data) {}
    public function resolveEndpoint(): string
    {
        return config('ichancy.endpoints.create_user');
    }
    protected function defaultBody(): array
    {
        return [
            "player" => [
                ...$this->data,
                'parentId' => config('ichancy.cashier.id')
            ]
        ];
    }
}
