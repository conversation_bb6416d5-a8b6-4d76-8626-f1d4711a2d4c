<?php

namespace App\BotApi\Requests;

use Saloon\Contracts\Body\HasBody;
use Saloon\Enums\Method;
use Saloon\Http\Request;
use Saloon\Traits\Body\HasJsonBody;

class CheckChannelMemberRequest extends Request implements HasBody
{
    use HasJsonBody;

    protected Method $method = Method::POST;

    public function __construct(protected array $data) {}
    public function resolveEndpoint(): string
    {
        return '/getChatMember';
    }
    protected function defaultBody(): array
    {
        return $this->data;
    }
}
