<?php

namespace App\BotApi\Requests;

use Saloon\Contracts\Body\HasBody;
use Saloon\Enums\Method;
use Saloon\Http\Request;
use Saloon\Traits\Body\HasJsonBody;

class GetPlayersRequest extends Request implements HasBody
{
    use HasJsonBody;

    protected Method $method = Method::POST;
    public function __construct(protected array $filter = []) {}
    public function resolveEndpoint(): string
    {
        return config('ichancy.endpoints.get_players');
    }
    protected function defaultBody(): array
    {
        return [
            "start" => 0,
            "limit" => 10,
            "filter" => $this->filter
        ];
    }
}
