<?php

namespace App\BotApi\Connectors;

use Saloon\Http\Connector;
use Saloon\Traits\Plugins\HasTimeout;

class PythonBotConnector extends Connector
{
    use HasTimeout;

    protected int $connectTimeout = 60;

    protected int $requestTimeout = 120;
    public function resolveBaseUrl(): string
    {
        return config('ichancy.python_bot_url');
    }
    protected function defaultHeaders(): array
    {
        return [
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
        ];
    }
}
