<?php

namespace App\BotApi\Connectors;

use Saloon\Http\Connector;

class CashierConnector extends Connector
{

    public function  __construct(protected ?string $cookie) {}
    public function resolveBaseUrl(): string
    {
        return config('ichancy.url');
    }

    protected function defaultHeaders(): array
    {
        return [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
            'Cookie' => $this->cookie
        ];
    }
}
