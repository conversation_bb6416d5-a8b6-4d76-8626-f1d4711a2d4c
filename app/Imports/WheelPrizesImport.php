<?php

namespace App\Imports;

use App\Models\WheelPrize;
use App\Models\WheelPrizeType;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;

class WheelPrizesImport implements ToCollection
{
    public function collection(Collection $collection)
    {
        foreach ($collection as $row) {
            WheelPrize::create([

                'wheel_prize_type_id' => $row[0],
                'value' => $row[1],
                'win_percent' => $row[2],
                'is_active' => $row[3],
                'sort' => $row[4],
            ]);
        }
    }


}
