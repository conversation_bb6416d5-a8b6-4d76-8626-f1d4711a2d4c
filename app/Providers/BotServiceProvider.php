<?php

namespace App\Providers;

use App\Channels\TelegramChannel;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\ServiceProvider;

class BotServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->bind('bot', fn() => new  \App\Helpers\Bot());
        $this->app->bind('bot_message', fn() => new  \App\Helpers\BotMessage());
        $this->app->bind('SettingService', fn() => new  \App\Services\SettingService());
        $this->app->bind('Cashier', fn() => new  \App\Helpers\Cashier());
        $this->app->bind('Logs', fn() => new  \App\Helpers\Logs());
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        Notification::extend('telegram', fn() => new TelegramChannel);
    }
}
