<?php

namespace App\Filament\Pages;

use App\Filament\Widgets\CashierStats;
use App\Filament\Widgets\FilterPaymentsWidget;
use App\Filament\Widgets\UserStat;
use Filament\Pages\Dashboard as BaseDashboard;

class Dashboard extends BaseDashboard
{
    protected static ?string $navigationIcon = 'heroicon-o-home';

    protected function getHeaderWidgets(): array
    {
        return [
            // UserStat::class,
            // CashierStats::class,
            // FilterPaymentsWidget::class,
        ];
    }

    public static function getNavigationLabel(): string
    {
        return __('Dashboard');
    }
}
