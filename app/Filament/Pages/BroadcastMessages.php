<?php

namespace App\Filament\Pages;

use App\Models\User;
use App\Notifications\BroadcastMessage;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Forms;
use Filament\Forms\Components\Actions;
use Filament\Notifications\Notification as NotificationsNotification;
use Filament\Pages\Page;
use Illuminate\Support\Facades\Notification;

class BroadcastMessages extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-envelope';

    protected static string $view = 'filament.pages.broadcast-messages';
    public array $data = [];
    public static function shouldRegisterNavigation(): bool
    {
        return false;
    }
    public static function getNavigationLabel(): string
    {
        return __("Broadcast messages");
    }
    public function getTitle(): string
    {

        return __("Broadcast messages");
    }
    public function mount()
    {
        $this->form->fill();
    }
    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('users')
                    ->translateLabel()
                    ->helperText(__("To send to all users leave it empty"))
                    ->options(User::where('telegram_id', '!=', null)->pluck('name', 'id'))
                    ->columnSpanFull()
                    ->multiple()
                    ->preload()
                    ->searchable(),
                Forms\Components\MarkdownEditor::make('message')
                    ->translateLabel()
                    ->required()
                    ->columnSpanFull()
            ])->statePath('data');
    }
    public function getFormActions()
    {
        return [
            Actions\Action::make('submit')
                ->translateLabel()
                ->submit('submit')
        ];
    }
    public function submit()
    {
        $data = $this->form->getState();
        if ($data['users']) {
            $users = User::whereIn('id', $data['users'])->get();
            Notification::send($users, new BroadcastMessage($data['message']));
        } else {
            User::where('telegram_id', '!=', null)
                ->chunk(50, fn($users) => Notification::send($users, new BroadcastMessage($data['message'])));
        }
        NotificationsNotification::make()
            ->title(__("Success"))
            ->success()
            ->send();
        $this->form->fill();
    }
}
