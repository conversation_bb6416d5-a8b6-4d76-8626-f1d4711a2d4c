<?php

namespace App\Filament\Pages;

use App\BotCommands\StartCommand;
use App\Facades\Bot;
use App\Facades\Settings as FacadesSettings;
use App\Models\Setting;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Forms;
use Filament\Forms\FormsComponent;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Facades\Log;
use Telegram\Bot\Api;
use Telegram\Bot\Objects\BotCommand;

class Settings extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';

    protected static string $view = 'filament.pages.settings';

    public ?array $data = [];
    // public static function shouldRegisterNavigation(): bool
    // {
    //     return false;
    // }
    public static function getNavigationLabel(): string
    {
        return __("Settings");
    }
    public function getTitle(): string|Htmlable
    {
        return __("Settings");
    }
    public function mount()
    {
        $settings = Setting::pluck('value', 'key')->toArray();
        if (!isset($settings['referral_percent'])) {
            $settings['referral_percent'] = 0;
        }
        if (!isset($settings['referral_calculation'])) {
            $settings['referral_calculation'] = 10;
        }
        if (!isset($settings['profit_type'])) {
            $settings['profit_type'] = 'on_burn';
        }
        if (!isset($settings['comession_type'])) {
            $settings['comession_type'] = 'percent';
        }
        $this->form->fill($settings);
    }

    public function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\Tabs::make()
                ->schema([
                    Forms\Components\Tabs\Tab::make('General')
                        ->translateLabel()
                        ->schema([
                            Forms\Components\Toggle::make('maintenance_mode')
                                ->translateLabel(),
                            Forms\Components\Toggle::make('allow_gift')
                                ->translateLabel(),
                            Forms\Components\Toggle::make('use_coupon_charge')
                                ->label(__("Disable coupon without charge")),
                            Forms\Components\Toggle::make('coupon_daily_charge')
                                ->label(__("Disable coupon without daily charge")),
                            Forms\Components\Toggle::make('allow_register_without_charge')
                                ->translateLabel(),
                            Forms\Components\TextInput::make('gift_commession')
                                ->translateLabel()
                                ->prefix('%')
                                ->numeric(),

                            Forms\Components\MarkdownEditor::make('channel_message')
                                ->translateLabel(),
                            Forms\Components\MarkdownEditor::make('maintenance_message')
                                ->translateLabel(),
                            Forms\Components\Textarea::make('terms')
                                ->translateLabel(),
                        ]),
                    Forms\Components\Tabs\Tab::make('Cashier')
                        ->translateLabel()
                        ->schema([
                            Forms\Components\TextInput::make('alarm_at')
                                ->translateLabel()
                                ->numeric(),
                            Forms\Components\TextInput::make('min_charge')
                                ->translateLabel()
                                ->numeric(),
                            Forms\Components\TextInput::make('min_withdraw')
                                ->translateLabel()
                                ->numeric(),
                            Forms\Components\TextInput::make('max_withdraw')
                                ->translateLabel()
                                ->numeric(),
                        ]),
                    Forms\Components\Tabs\Tab::make('Notifications')
                        ->translateLabel()
                        ->schema([
                            Forms\Components\TextInput::make('min_important_charge')
                                ->label("Min important charge value")
                                ->translateLabel()
                                ->numeric()
                        ]),
                    Forms\Components\Tabs\Tab::make('Referral')
                        ->translateLabel()
                        ->schema([
                            Forms\Components\TextInput::make('referral_percent')
                                ->numeric()
                                ->default(0)
                                ->translateLabel(),
                            Forms\Components\Select::make('comession_type')
                                ->label("Comession type")
                                ->translateLabel()
                                ->options([
                                    'percent' => __('Percent'),
                                    'fixed' => __('Fixed'),
                                ]),
                            Forms\Components\Select::make('profit_type')
                                ->translateLabel()
                                ->options([
                                    'on_charge' => __('On charge'),
                                    'on_burn' => __('On burn'),
                                    'on_ref' => __("On referral"),
                                ])->live()
                                ->afterStateUpdated(function ($state, $set) {
                                    if ($state == 'on_ref') {
                                        $set('comession_type', 'fixed');
                                    }
                                }),
                            Forms\Components\TextInput::make('min_ref_charge')
                                ->label("Min charge amount to activate profit")
                                ->translateLabel()
                                ->numeric()
                                ->hidden(fn($get) => $get('profit_type') != 'on_ref'),
                            Forms\Components\TextInput::make('referral_calculation')
                                ->translateLabel()
                                ->helperText(__("calculate profit period in days"))
                                ->numeric()
                                ->suffix(__("days")),
                            Forms\Components\MarkdownEditor::make('referral_message')
                                ->translateLabel(),
                        ]),
                    Forms\Components\Tabs\Tab::make('payment')
                        ->translateLabel()
                        ->schema([
                            Forms\Components\Toggle::make('auto_decline')
                                ->label('Auto decline payment requests for syriatel cash and bemo?')
                                ->translateLabel()

                        ]),
                    Forms\Components\Tabs\Tab::make('Wheel')
                        ->translateLabel()
                        ->schema([
                            Forms\Components\Toggle::make('enable_wheel')
                                ->translateLabel(),
                            Forms\Components\TextInput::make('wheel_min_charge')
                                ->label("Min charge")
                                ->translateLabel()
                                ->numeric(),
                            Forms\Components\Grid::make()
                                ->schema([
                                    Forms\Components\ColorPicker::make('wheel_main_color'),
                                    Forms\Components\ColorPicker::make('wheel_secondary_color'),
                                ]),
                            Forms\Components\FileUpload::make('wheel_btn')
                                ->image()
                                ->directory('wheel')
                                ->maxSize(2048),
                            Forms\Components\FileUpload::make('wheel_pin')
                                ->image()
                                ->directory('wheel')
                                ->maxSize(2048),
                            Forms\Components\FileUpload::make('wheel_stroke')
                                ->image()
                                ->directory('wheel')
                                ->maxSize(2048),
                            Forms\Components\FileUpload::make('wheel_background')
                                ->image()
                                ->directory('wheel')
                                ->maxSize(2048)
                        ]),
                    Forms\Components\Tabs\Tab::make('Ichancy')
                        ->translateLabel()
                        ->schema([
                            Forms\Components\Toggle::make('ichancy_charge_enabled')
                                ->translateLabel(),
                            Forms\Components\Toggle::make('ichancy_withdraw_enabled')
                                ->translateLabel(),
                            Forms\Components\Toggle::make('ichancy_register_enabled')
                                ->translateLabel(),
                        ])
                    //     Forms\Components\Tabs\Tab::make('Telegram')
                    //         ->translateLabel()
                    //         ->schema([
                    //             Forms\Components\TextInput::make('admin_group_id')
                    //                 ->translateLabel(),
                    //             Forms\Components\TextInput::make('withdraw_group_id')
                    //                 ->translateLabel(),
                    //             Forms\Components\TextInput::make('support_group_id')
                    //                 ->translateLabel(),
                    //             Forms\Components\TextInput::make('send_logs_to')
                    //                 ->translateLabel(),
                    //             Forms\Components\TextInput::make('ichancy_logs')
                    //                 ->translateLabel(),
                    //             // Forms\Components\TextInput::make('cookies'),
                    //             // Forms\Components\TextInput::make('captcha_enabled'),
                    //         ])
                ])
        ])
            ->statePath('data');
    }

    public function getFormActions()
    {
        return [
            Forms\Components\Actions\Action::make('submit')
                ->translateLabel()
                ->submit('submit')
        ];
    }

    public function submit()
    {
        $data = $this->form->getState();

        foreach ($data as $key => $value) {
            FacadesSettings::set($key, $value);
            switch ($key) {
                case 'bot_description':
                    if ($value) {
                        Bot::setBotDescription($value);
                    }
                    break;
                case 'bot_name':
                    if ($value) {
                        Bot::setBotName($value);
                    }
                    break;
            }
        }

        Notification::make()
            ->title(__("Saved"))
            ->success()
            ->send();
    }
}
