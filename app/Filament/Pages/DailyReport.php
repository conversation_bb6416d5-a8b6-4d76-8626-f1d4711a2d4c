<?php

namespace App\Filament\Pages;

use App\Services\DailyLogService;
use Carbon\Carbon;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Illuminate\Support\Facades\Log;

class DailyReport extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-document-chart-bar';

    protected static string $view = 'filament.pages.daily-report';

    protected static ?string $navigationGroup = 'Reports';

    public ?array $data = [];

    public function mount(): void
    {
        $this->form->fill([
            'date' => Carbon::yesterday()->format('Y-m-d'),
        ]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                DatePicker::make('date')
                    ->translateLabel()
                    ->required()
                    ->default(Carbon::yesterday()),
            ])
            ->statePath('data');
    }

    public function generateReport(): void
    {
        $date = Carbon::parse($this->data['date']);

        try {
            app(DailyLogService::class)->dailyLog($date);

            Notification::make()
                ->title(__('Report sent successfully'))
                ->success()
                ->send();
        } catch (\Throwable $e) {
            Log::error('Failed to send report to Telegram: ' . $e->getMessage());

            Notification::make()
                ->title(__('Failed to send report'))
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }

    public static function getNavigationLabel(): string
    {
        return __('Daily report');
    }
}
