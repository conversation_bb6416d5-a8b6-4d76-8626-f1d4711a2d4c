<?php

namespace App\Filament\Pages;

use App\Facades\Settings;
use App\Models\Setting;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Forms;
use Filament\Forms\Components\Actions;
use Filament\Notifications\Notification;
use Filament\Pages\Page;

class CashbackSettings extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-banknotes';

    protected static string $view = 'filament.pages.cashback-settings';

    public ?array $data = [];
    public static function shouldRegisterNavigation(): bool
    {
        return false;
    }
    public function mount()
    {
        $settings = Setting::whereIn('key', ['enable_cashback', 'min_cashback', 'cashback_percent'])
            ->pluck('value', 'key');
        $this->form->fill($settings->toArray());
    }
    public static function getNavigationLabel(): string
    {
        return __("Cashback settings");
    }

    public function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\Toggle::make('enable_cashback')
                ->translateLabel(),
            Forms\Components\TextInput::make('min_cashback')
                ->translateLabel(),
            Forms\Components\TextInput::make('cashback_percent')
                ->translateLabel()
                ->prefix('%')
        ])->statePath('data');
    }

    public function getFormActions()
    {
        return [
            Actions\Action::make('submit')
                ->translateLabel()
                ->submit('submit')
        ];
    }
    public function submit()
    {
        $data = $this->form->getState();
        foreach ($data as $key => $value) {
            Settings::set($key, $value);
        }
        Notification::make()
            ->title(__("Saved"))
            ->success()
            ->send();
    }
}
