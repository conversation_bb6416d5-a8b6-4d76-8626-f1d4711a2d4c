<?php

namespace App\Filament\Resources;

use App\Enums\SyriatelCodeType;
use App\Filament\Resources\SyriatelCodeResource\Pages;
use App\Filament\Resources\SyriatelCodeResource\RelationManagers;
use App\Models\SyriatelCode;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class SyriatelCodeResource extends Resource
{
    protected static ?string $model = SyriatelCode::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function getNavigationLabel(): string
    {
        return __("Syriatel codes");
    }
    public static function getPluralLabel(): ?string
    {
        return __("Syriatel codes");
    }
    public static function getModelLabel(): string
    {
        return __("Syriatel code");
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('code')
                    ->translateLabel()
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('phone')
                    ->translateLabel()
                    ->tel()
                    ->required()
                    ->maxLength(255),
                Forms\Components\Select::make('type')
                    ->translateLabel()
                    ->options(SyriatelCodeType::getAsOptions())
                    ->required(),
                // Forms\Components\TextInput::make('amount')
                //     ->translateLabel()
                //     ->numeric(),
                // Forms\Components\TextInput::make('max_amount')
                //     ->translateLabel()
                //     ->numeric(),
                Forms\Components\Toggle::make('is_active')
                    ->translateLabel()
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('code')
                    ->translateLabel()
                    ->searchable(),
                Tables\Columns\TextColumn::make('phone')
                    ->translateLabel()
                    ->searchable(),
                Tables\Columns\TextColumn::make('type')
                    ->translateLabel(),
                Tables\Columns\ToggleColumn::make('is_active')
                    ->translateLabel(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSyriatelCodes::route('/'),
            'create' => Pages\CreateSyriatelCode::route('/create'),
            'edit' => Pages\EditSyriatelCode::route('/{record}/edit'),
        ];
    }
}
