<?php

namespace App\Filament\Resources;

use App\Exports\WheelPrizesExport;
use App\Exports\WheelPrizesTemplateExport;
use App\Filament\Resources\WheelPrizeResource\Pages;
use App\Filament\Resources\WheelPrizeResource\RelationManagers;
use App\Imports\WheelPrizesImport;
use App\Models\WheelPrize;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Maatwebsite\Excel\Facades\Excel;

class WheelPrizeResource extends Resource
{
    protected static ?string $model = WheelPrize::class;

    protected static ?string $navigationIcon = 'heroicon-o-gift-top';
    // public static function shouldRegisterNavigation(): bool
    // {
    //     return false;
    // }
    public static function getNavigationLabel(): string
    {
        return __('Wheel prizes');
    }
    public static function getModelLabel(): string
    {
        return __("Wheel prize");
    }
    public static function getPluralLabel(): ?string
    {
        return __("Wheel prizes");
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([

                Forms\Components\Select::make('wheel_prize_type_id')
                    ->label(__("Wheel prize type"))
                    ->translateLabel()
                    ->required()
                    ->relationship('wheelPrizeType', 'name')
                    ->preload()
                    ->searchable(),
                Forms\Components\TextInput::make('win_percent')
                    ->translateLabel()
                    ->numeric()
                    ->default(0)
                    ->suffix('%'),
                Forms\Components\TextInput::make('value')
                    ->translateLabel()
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('sort')
                    ->label(__('Sort order'))
                    ->translateLabel()
                    ->numeric()
                    ->default(0),
                Forms\Components\Toggle::make('is_active')
                    ->translateLabel()
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->reorderable('sort')
            ->defaultSort('sort')
            ->columns([

                Tables\Columns\TextColumn::make('wheelPrizeType.name')
                    ->translateLabel()
                    ->sortable(),
                Tables\Columns\TextColumn::make('win_percent')
                    ->translateLabel()
                    ->suffix('%'),
                Tables\Columns\TextColumn::make('value')
                    ->translateLabel()
                    ->searchable(),
                Tables\Columns\TextColumn::make('sort')
                    ->label(__('Sort order'))
                    ->translateLabel()
                    ->sortable(),
                Tables\Columns\ToggleColumn::make('is_active')
                    ->translateLabel(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\Action::make('export')
                    ->label(__('Export to Excel'))
                    ->icon('heroicon-o-arrow-down-tray')
                    ->color('success')
                    ->action(function () {
                        return Excel::download(new WheelPrizesExport, 'wheel-prizes-' . now()->format('Y-m-d') . '.xlsx');
                    }),
                Tables\Actions\Action::make('download_template')
                    ->label(__('Download Template'))
                    ->icon('heroicon-o-document-arrow-down')
                    ->color('info')
                    ->action(function () {
                        return Excel::download(new WheelPrizesTemplateExport, 'wheel-prizes-template.xlsx');
                    }),
                Tables\Actions\Action::make('import')
                    ->label(__('Import from Excel'))
                    ->icon('heroicon-o-arrow-up-tray')
                    ->color('primary')
                    ->form([
                        Forms\Components\FileUpload::make('file')
                            ->label(__('Excel File'))
                            ->acceptedFileTypes(['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel'])
                            ->required()
                            ->directory('imports')
                            ->visibility('private')
                            ->helperText(__('Upload an Excel file with wheel prizes data. The file should have the same format as the exported file.')),
                    ])
                    ->action(function (array $data) {
                        try {
                            // Get the uploaded file path - Filament stores it in storage/app/imports/
                            $fileName = $data['file'];
                            $filePath = storage_path('app/imports/' . $fileName);

                            // Check if file exists
                            if (!file_exists($filePath)) {
                                // Try alternative paths
                                $alternativePaths = [
                                    storage_path('app/public/imports/' . $fileName),
                                    storage_path('app/' . $fileName),
                                    storage_path('app/public/' . $fileName),
                                ];

                                foreach ($alternativePaths as $altPath) {
                                    if (file_exists($altPath)) {
                                        $filePath = $altPath;
                                        break;
                                    }
                                }
                            }

                            if (!file_exists($filePath)) {
                                throw new \Exception('Uploaded file not found. Please try uploading again.');
                            }

                            Excel::import(new WheelPrizesImport, $filePath);

                            // Clean up the uploaded file
                            if (file_exists($filePath)) {
                                unlink($filePath);
                            }

                            Notification::make()
                                ->title(__('Import successful'))
                                ->body(__('Wheel prizes have been imported successfully.'))
                                ->success()
                                ->send();
                        } catch (\Exception $e) {
                            Notification::make()
                                ->title(__('Import failed'))
                                ->body(__('An error occurred while importing: ') . $e->getMessage())
                                ->danger()
                                ->send();
                        }
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListWheelPrizes::route('/'),
            'create' => Pages\CreateWheelPrize::route('/create'),
            'edit' => Pages\EditWheelPrize::route('/{record}/edit'),
        ];
    }
}
