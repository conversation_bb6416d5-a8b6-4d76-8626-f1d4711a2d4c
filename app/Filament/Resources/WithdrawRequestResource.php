<?php

namespace App\Filament\Resources;

use App\Filament\Resources\WithdrawRequestResource\Pages;
use App\Filament\Resources\WithdrawRequestResource\RelationManagers;
use App\Helpers\ChargeRequestStatus;
use App\Models\WithdrawRequest;
use App\Services\WithdrawRequestService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class WithdrawRequestResource extends Resource
{
    protected static ?string $model = WithdrawRequest::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    // public static function shouldRegisterNavigation(): bool
    // {
    //     return false;
    // }
    public static function getNavigationLabel(): string
    {
        return __("Withdraw Requests");
    }
    public static function getPluralLabel(): ?string
    {
        return __("Withdraw Requests");
    }
    public static function getModelLabel(): string
    {
        return __("Withdraw Request");
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('status')
                    ->options(ChargeRequestStatus::getAsOptions())
                    ->columnSpan(2)
                    ->live(),
                Forms\Components\MarkdownEditor::make('message')
                    ->hidden(fn($get) => $get('status') != 'rejected')
                    ->columnSpan(2)
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('created_at', 'desc')
            ->columns([
                Tables\Columns\TextColumn::make('user.name')
                    ->searchable()
                    ->translateLabel()
                    ->sortable(),
                Tables\Columns\TextColumn::make('user.telegram_id')
                    ->label('Telegram id')
                    ->searchable()
                    ->translateLabel()
                    ->sortable(),
                Tables\Columns\TextColumn::make('withdrawMethod.name')
                    ->translateLabel()
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('amount')
                    ->translateLabel()
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('payment_info')
                    ->copyable()
                    ->label('Payment number or account address')
                    ->translateLabel(),
                Tables\Columns\TextColumn::make('operation_id')
                    ->copyable()
                    ->translateLabel()
                    ->searchable(),
                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->formatStateUsing(fn($state) => __(ucwords($state)))
                    ->translateLabel()
                    ->sortable()
                    ->badge()
                    ->colors([
                        'warning' => 'pending',
                        'success' => 'approved',
                        'danger' => 'rejected',
                    ]),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime('Y-m-d H:i')
                    ->searchable()
                    ->sortable()

            ])
            ->filters([
                //
            ])
            ->actions([
                // Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('edit')
                    ->label(__('Edit'))
                    ->icon('heroicon-o-pencil-square')
                    ->form([
                        Forms\Components\Select::make('status')
                            ->options(ChargeRequestStatus::getAsOptions())
                            ->columnSpan(2)
                            ->live(),
                        Forms\Components\MarkdownEditor::make('message')
                            ->hidden(fn($get) => $get('status') != 'rejected')
                            ->columnSpan(2)
                    ])->action(function ($record, $data) {
                        $withdrawRequestService = app()->make(WithdrawRequestService::class);
                        $record->update($data);
                        $withdrawRequestService->sendStatusMessage($record, $record->user->telegram_id);
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListWithdrawRequests::route('/'),
            'create' => Pages\CreateWithdrawRequest::route('/create'),
            'edit' => Pages\EditWithdrawRequest::route('/{record}/edit'),
        ];
    }
}
