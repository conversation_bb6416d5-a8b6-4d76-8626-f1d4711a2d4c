<?php

namespace App\Filament\Resources;

use App\Filament\Resources\GiftLogResource\Pages;
use App\Filament\Resources\GiftLogResource\RelationManagers;
use App\Models\GiftLog;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class GiftLogResource extends Resource
{
    protected static ?string $model = GiftLog::class;

    protected static ?string $navigationIcon = 'heroicon-o-gift';

    public static function getNavigationLabel(): string
    {
        return __('Gift logs');
    }
    public static function getModelLabel(): string
    {
        return __("Gift log");
    }
    public static function getPluralLabel(): ?string
    {
        return __("Gift logs");
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('sender_id')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('receiver_id')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('amount')
                    ->required()
                    ->numeric(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('created_at', 'desc')
            ->columns([
                Tables\Columns\TextColumn::make('sender.telegram_id')
                    ->translateLabel()
                    ->sortable()
                    ->copyable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('receiver.telegram_id')
                    ->translateLabel()
                    ->copyable()
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('amount')
                    ->translateLabel()
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->translateLabel()
                    ->dateTime('Y-m-d H:i')
                    ->sortable(),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListGiftLogs::route('/'),
            'create' => Pages\CreateGiftLog::route('/create'),
            'edit' => Pages\EditGiftLog::route('/{record}/edit'),
        ];
    }
}
