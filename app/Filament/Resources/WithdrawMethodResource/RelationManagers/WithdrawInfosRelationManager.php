<?php

namespace App\Filament\Resources\WithdrawMethodResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class WithdrawInfosRelationManager extends RelationManager
{
    protected static string $relationship = 'withdrawInfos';
    protected static bool $isLazy = false;

    public static function getTitle(Model $ownerRecord, string $pageClass): string
    {
        return __("Withdraw Info");
    }
    public static function getModelLabel(): ?string
    {
        return __('Withdraw Info');
    }
    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('key')
                    ->translateLabel()
                    ->required()
                    ->disabledOn('edit')
                    ->maxLength(255)
                    ->columnSpan(2),
                Forms\Components\MarkdownEditor::make('value')
                    ->translateLabel()
                    ->required()
                    ->columnSpan(2),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('key')
            ->columns([
                Tables\Columns\TextColumn::make('key')
                    ->translateLabel(),
                Tables\Columns\TextColumn::make('value')
                    ->translateLabel(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->hidden(fn($livewire) => $livewire->ownerRecord->withdrawInfos->count() > 0),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([]);
    }
}
