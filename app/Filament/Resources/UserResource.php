<?php

namespace App\Filament\Resources;

use App\Filament\Resources\UserResource\Pages;
use App\Filament\Resources\UserResource\RelationManagers;
use App\Filament\Resources\UserResource\RelationManagers\IchancyAccountRelationManager;
use App\Filament\Resources\UserResource\RelationManagers\ReferralsRelationManager;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class UserResource extends Resource
{
    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-user';

    // public static function shouldRegisterNavigation(): bool
    // {
    //     return false;
    // }
    public static function getNavigationLabel(): string
    {
        return __('Users');
    }
    public static function getModelLabel(): string
    {
        return __("User");
    }
    public static function getPluralLabel(): ?string
    {
        return __("Users");
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->translateLabel()
                    ->maxLength(255),
                Forms\Components\TextInput::make('email')
                    ->translateLabel()
                    ->email()
                    ->maxLength(255),
                Forms\Components\TextInput::make('password')
                    ->translateLabel()
                    ->password()
                    ->maxLength(255),
                Forms\Components\TextInput::make('telegram_id')
                    ->translateLabel()
                    ->tel()
                    ->maxLength(255),
                Forms\Components\TextInput::make('ref_percent')
                    ->translateLabel()
                    ->numeric()
                    ->prefix('%'),
                Forms\Components\Select::make('user_id')
                    ->label(__("Ref user"))
                    ->translateLabel()
                    ->relationship('user', 'telegram_id', modifyQueryUsing: fn($query) => $query->whereNotNull('telegram_id'))
                    ->preload()
                    ->searchable(),
                Forms\Components\Toggle::make('is_admin')
                    ->default(false)
                    ->translateLabel(),
                Forms\Components\Toggle::make('is_active')
                    ->default(false)
                    ->translateLabel(),
                Forms\Components\Repeater::make('userBalance')
                    ->relationship()
                    ->maxItems(1)
                    ->columnSpanFull()
                    ->deletable(false)
                    ->schema([
                        Forms\Components\TextInput::make('active')
                            ->label('Balance')
                            ->translateLabel()
                    ])
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn($query) => $query->where('email', null)->with('referrals')
                ->withCount('referrals'))
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->translateLabel()
                    ->searchable(),
                Tables\Columns\TextColumn::make('telegram_id')
                    ->translateLabel()
                    ->searchable()
                    ->copyable(),
                Tables\Columns\TextColumn::make('user.telegram_id')
                    ->translateLabel()
                    ->searchable()
                    ->copyable(),
                Tables\Columns\TextColumn::make('ichancyAccount.player_id')
                    ->label(__("Player Id"))
                    ->translateLabel()
                    ->copyable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('ichancyAccount.username')
                    ->label(__("username"))
                    ->translateLabel()
                    ->copyable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('userBalance.active')
                    ->formatStateUsing(fn($state) => $state ?? '0')
                    ->sortable()
                    ->prefix('ليرة')
                    ->label(__("User balance in bot"))
                    ->translateLabel()
                    ->searchable(),
                Tables\Columns\TextColumn::make('ref_percent')
                    ->translateLabel()
                    ->numeric()
                    ->prefix('%'),
                Tables\Columns\TextColumn::make('referrals_count')
                    ->translateLabel()
                    ->label('Referrals')
                    ->sortable()
                // Tables\Columns\ToggleColumn::make('is_active')
                //     ->translateLabel()

            ])
            ->filters([
                //
            ])
            ->actions([
                // Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('zero')
                    ->translateLabel()
                    ->requiresConfirmation()
                    ->action(fn($record) => $record->userBalance()->update(['active' => 0]))
            ])
            ->bulkActions([
                // Tables\Actions\BulkActionGroup::make([
                //     Tables\Actions\DeleteBulkAction::make(),
                // ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            IchancyAccountRelationManager::class,
            ReferralsRelationManager::class
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUsers::route('/'),
            'create' => Pages\CreateUser::route('/create'),
            'edit' => Pages\EditUser::route('/{record}/edit'),
        ];
    }
}
