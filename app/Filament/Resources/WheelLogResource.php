<?php

namespace App\Filament\Resources;

use App\Filament\Resources\WheelLogResource\Pages;
use App\Filament\Resources\WheelLogResource\RelationManagers;
use App\Models\WheelLog;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class WheelLogResource extends Resource
{
    protected static ?string $model = WheelLog::class;

    protected static ?string $navigationIcon = 'heroicon-o-clipboard-document-list';

    public static function getNavigationLabel(): string
    {
        return __('Wheel logs');
    }
    public static function getModelLabel(): string
    {
        return __("Wheel log");
    }
    public static function getPluralLabel(): ?string
    {
        return __("Wheel logs");
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('user_id')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('wheel_prize_id')
                    ->required()
                    ->numeric(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('created_at', 'desc')
            ->columns([
                Tables\Columns\TextColumn::make('user.name')
                    ->searchable()
                    ->translateLabel()
                    ->sortable(),
                Tables\Columns\TextColumn::make('user.telegram_id')
                    ->searchable()
                    ->label("Telegram id")
                    ->translateLabel()
                    ->copyable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('wheelPrize.wheelPrizeType.name')
                    ->translateLabel()
                    ->sortable(),
                Tables\Columns\TextColumn::make('wheelPrize.value')
                    ->translateLabel()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->translateLabel()
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\Filter::make('date')
                    ->form([
                        Forms\Components\DatePicker::make('created_at')
                            ->translateLabel(),
                    ])->query(fn($query, $data) => $query->when(isset($data['created_at']), fn($q) => $q->whereDate('created_at', $data['created_at'])))
            ])
            ->actions([])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListWheelLogs::route('/'),
            'create' => Pages\CreateWheelLog::route('/create'),
            'edit' => Pages\EditWheelLog::route('/{record}/edit'),
        ];
    }
}
