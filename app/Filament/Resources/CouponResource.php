<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CouponResource\Pages;
use App\Filament\Resources\CouponResource\RelationManagers;
use App\Models\Coupon;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Collection;

class CouponResource extends Resource
{
    protected static ?string $model = Coupon::class;

    protected static ?string $navigationIcon = 'heroicon-o-gift';
    // public static function shouldRegisterNavigation(): bool
    // {
    //     return false;
    // }
    public static function getNavigationLabel(): string
    {
        return __('Coupons');
    }
    public static function getModelLabel(): string
    {
        return __("Coupon");
    }
    public static function getPluralLabel(): ?string
    {
        return __("Coupons");
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('code')
                    ->translateLabel()
                    ->required()
                    ->live(onBlur: true)
                    ->maxLength(255)
                    ->unique('coupons', 'code', ignoreRecord: true),
                // Forms\Components\Select::make('type')
                //     ->translateLabel()
                //     ->options([
                //         'percent' => __("percent"),
                //         'fixed' => __("fixed"),
                //     ])
                //     ->required(),
                Forms\Components\TextInput::make('value')
                    ->translateLabel()
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('usage_times')
                    ->translateLabel()
                    ->numeric()
                    ->required(),
                // Forms\Components\DatePicker::make('expired_at')
                //     ->columnSpanFull()
                //     ->translateLabel(),
                Forms\Components\Toggle::make('is_active')
                    ->translateLabel()
                    ->required(),
                // Forms\Components\Toggle::make('multiple_use')
                //     ->translateLabel()
                //     ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('code')
                    ->translateLabel()
                    ->copyable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('value')
                    ->translateLabel()
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('expired_at')
                    ->translateLabel()
                    ->date()
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_active')
                    ->translateLabel()
                    ->boolean(),
                Tables\Columns\IconColumn::make('multiple_use')
                    ->translateLabel()
                    ->boolean(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('activate')
                        ->label(__('Activate'))
                        ->icon('heroicon-o-check-circle')
                        ->color('success')
                        ->requiresConfirmation()
                        ->action(function (Collection $records): void {
                            $records->each(function ($record): void {
                                $record->update(['is_active' => true]);
                            });

                            Notification::make()
                                ->title(__(':count coupons activated', ['count' => $records->count()]))
                                ->success()
                                ->send();
                        }),
                    Tables\Actions\BulkAction::make('deactivate')
                        ->label(__('Deactivate'))
                        ->icon('heroicon-o-x-circle')
                        ->color('danger')
                        ->requiresConfirmation()
                        ->action(function (Collection $records): void {
                            $records->each(function ($record): void {
                                $record->update(['is_active' => false]);
                            });

                            Notification::make()
                                ->title(__(':count coupons deactivated', ['count' => $records->count()]))
                                ->success()
                                ->send();
                        }),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCoupons::route('/'),
            'create' => Pages\CreateCoupon::route('/create'),
            'edit' => Pages\EditCoupon::route('/{record}/edit'),
        ];
    }
}
