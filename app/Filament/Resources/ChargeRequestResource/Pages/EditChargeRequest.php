<?php

namespace App\Filament\Resources\ChargeRequestResource\Pages;

use App\Filament\Resources\ChargeRequestResource;
use App\Helpers\ChargeRequestStatus;
use App\Services\ChargeRequestService;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditChargeRequest extends EditRecord
{
    protected static string $resource = ChargeRequestResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
    protected function afterSave()
    {
        $chargeRequestService = app()->make(ChargeRequestService::class);
        if ($this->record->status == ChargeRequestStatus::APPROVED->value && !$this->record->is_charged) {

            $prev = $this->record->user->userBalance?->active ?? 0;
            $this->record->user->userBalance()->updateOrCreate([
                'active' => $prev + $this->record->amount,
            ]);
            $this->record->is_charged = true;
            $this->record->save();
        }
        $chargeRequestService->sendStatusMessage($this->record, $this->record->user->telegram_id);
    }
}
