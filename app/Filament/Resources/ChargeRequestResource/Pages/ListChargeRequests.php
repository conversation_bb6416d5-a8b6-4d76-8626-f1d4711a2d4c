<?php

namespace App\Filament\Resources\ChargeRequestResource\Pages;

use App\Filament\Resources\ChargeRequestResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListChargeRequests extends ListRecords
{
    protected static string $resource = ChargeRequestResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make(),
        ];
    }
}
