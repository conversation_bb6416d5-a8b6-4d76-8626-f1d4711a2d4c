<?php

namespace App\Filament\Resources;

use App\Filament\Resources\PaymentMethodResource\Pages;
use App\Filament\Resources\PaymentMethodResource\RelationManagers;
use App\Filament\Resources\PaymentMethodResource\RelationManagers\InstructionsRelationManager;
use App\Filament\Resources\PaymentMethodResource\RelationManagers\PaymentInfosRelationManager;
use App\Models\PaymentMethod;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class PaymentMethodResource extends Resource
{
    protected static ?string $model = PaymentMethod::class;

    protected static ?string $navigationIcon = 'heroicon-o-currency-dollar';


    public static function getNavigationLabel(): string
    {
        return __("Payment methods");
    }
    public static function getPluralLabel(): ?string
    {
        return __("Payment methods");
    }
    public static function getModelLabel(): string
    {
        return __("Payment method");
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->translateLabel()
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('slug')
                    ->translateLabel()
                    ->required()
                    ->disabledOn('edit')
                    ->maxLength(255),
                Forms\Components\TextInput::make('min_value')
                    ->label(__("Min charge value"))
                    ->translateLabel()
                    ->numeric()
                    ->default(0),
                Forms\Components\TextInput::make('exchange')
                    ->translateLabel()
                    ->numeric()
                    ->default(1),
                Forms\Components\Textarea::make('disable_reply')
                    ->translateLabel()
                    ->columnSpanFull()

                    ->label('Maintenance message'),
                Forms\Components\Toggle::make('is_active')
                    ->translateLabel()
                    ->required(),
                Forms\Components\Toggle::make('allow_repeat')
                    ->translateLabel()
                    ->required(),
                Forms\Components\Section::make(__('Cashback Settings'))
                    ->schema([
                        Forms\Components\Toggle::make('enable_cashback')
                            ->translateLabel()
                            ->default(false),
                        Forms\Components\TextInput::make('cashback_percent')
                            ->translateLabel()
                            ->numeric()
                            ->default(0)
                            ->suffix('%')
                            ->helperText(__('Percentage of cashback to be added to user account after charge')),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->translateLabel()
                    ->searchable(),
                Tables\Columns\TextColumn::make('min_value')
                    ->label(__("Min charge value"))
                    ->translateLabel(),
                Tables\Columns\TextColumn::make('exchange')
                    ->translateLabel()
                    ->numeric(),
                Tables\Columns\ToggleColumn::make('is_active')
                    ->translateLabel(),
                Tables\Columns\ToggleColumn::make('in_maintenance')
                    ->translateLabel(),
                Tables\Columns\ToggleColumn::make('enable_cashback')
                    ->translateLabel(),
                Tables\Columns\TextColumn::make('cashback_percent')
                    ->translateLabel()
                    ->suffix('%')
                    ->visible(fn($record) => $record?->enable_cashback),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            PaymentInfosRelationManager::class,
            InstructionsRelationManager::class
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPaymentMethods::route('/'),
            'create' => Pages\CreatePaymentMethod::route('/create'),
            'edit' => Pages\EditPaymentMethod::route('/{record}/edit'),
        ];
    }
}
