<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ChargeRequestResource\Pages;
use App\Filament\Resources\ChargeRequestResource\RelationManagers;
use App\Helpers\ChargeRequestStatus;
use App\Models\ChargeRequest;
use App\Services\ChargeRequestService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ChargeRequestResource extends Resource
{
    protected static ?string $model = ChargeRequest::class;

    protected static ?string $navigationIcon = 'heroicon-o-currency-dollar';
   // public static function shouldRegisterNavigation(): bool
   // {
      //  return false;
    //}
    public static function getNavigationLabel(): string
    {
        return __('Charge Requests');
    }
    public static function getModelLabel(): string
    {
        return __('Charge Request');
    }
    public static function getPluralLabel(): string
    {
        return __('Charge Requests');
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('status')
                    ->options(ChargeRequestStatus::getAsOptions())
                    ->columnSpan(2)
                    ->live(),
                Forms\Components\MarkdownEditor::make('message')
                    ->hidden(fn($get) => $get('status') != 'rejected')
                    ->columnSpan(2)
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('created_at', 'desc')
            ->columns([
                Tables\Columns\TextColumn::make('user.name')
                    ->translateLabel()
                    ->searchable(),
                Tables\Columns\TextColumn::make('user.telegram_id')
                    ->copyable()
                    ->searchable()
                    ->sortable()
                    ->label('Telegram id')
                    ->translateLabel(),
                Tables\Columns\TextColumn::make('paymentMethod.name')
                    ->searchable()
                    ->translateLabel()
                    ->sortable(),
                Tables\Columns\TextColumn::make('operation_id')
                    ->copyable()
                    ->searchable()
                    ->translateLabel(),
                Tables\Columns\TextColumn::make('payment_info')
                    ->copyable()
                    ->label('Payment number or account address')
                    ->translateLabel()
                    ->searchable(),
                Tables\Columns\TextColumn::make('amount')
                    ->translateLabel()
                    ->copyable()
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->formatStateUsing(fn($state) => __(ucwords($state->value)))
                    ->translateLabel()
                    ->sortable()
                    ->badge()
                    ->colors([
                        'warning' => 'pending',
                        'success' => 'approved',
                        'danger' => 'rejected',
                    ]),
                Tables\Columns\TextColumn::make('created_at')
                    ->translateLabel()
                    ->dateTime('Y-m-d H:i', 'Asia/Riyadh')

            ])
            ->filters([
                Tables\Filters\Filter::make('date')
                    ->form([
                        Forms\Components\DatePicker::make('created_at')
                            ->translateLabel(),
                        Forms\Components\Select::make('status')
                            ->translateLabel()
                            ->options(ChargeRequestStatus::getAsOptions()),
                    ])
                    ->query(fn($query, $data) => $query->when(isset($data['created_at']), fn($q) => $q->whereDate('created_at', $data['created_at']))
                        ->when(isset($data['status']), fn($q) => $q->where('status', $data['status'])))
            ])
            ->actions([
                // Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('edit')
                    ->label(__('Edit'))
                    ->icon('heroicon-o-pencil-square')
                    ->form([
                        Forms\Components\Select::make('status')
                            ->options(ChargeRequestStatus::getAsOptions())
                            ->columnSpan(2)
                            ->live(),
                        Forms\Components\MarkdownEditor::make('message')
                            ->hidden(fn($get) => $get('status') != 'rejected')
                            ->columnSpan(2)
                    ])->action(function ($record, $data) {
                        $chargeRequestService = app()->make(ChargeRequestService::class);
                        $record->update($data);
                        if ($data['status'] == ChargeRequestStatus::APPROVED->value && !$record->is_charged) {
                            $prev = $record->user->userBalance?->active ?? 0;
                            $userBalance = $record->user->userBalance()->firstOrCreate();
                            $userBalance->update([
                                'active' => $prev + $record->amount,
                            ]);
                            $record->is_charged = true;
                            $record->save();
                        }
                        $chargeRequestService->sendStatusMessage($record, $record->user->telegram_id);
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListChargeRequests::route('/'),
            'create' => Pages\CreateChargeRequest::route('/create'),
            'edit' => Pages\EditChargeRequest::route('/{record}/edit'),
        ];
    }
}
