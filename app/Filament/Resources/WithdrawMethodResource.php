<?php

namespace App\Filament\Resources;

use App\Enums\CommessionType;
use App\Filament\Resources\WithdrawMethodResource\Pages;
use App\Filament\Resources\WithdrawMethodResource\RelationManagers;
use App\Filament\Resources\WithdrawMethodResource\RelationManagers\WithdrawInfosRelationManager;
use App\Models\WithdrawMethod;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class WithdrawMethodResource extends Resource
{
    protected static ?string $model = WithdrawMethod::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    // public static function shouldRegisterNavigation(): bool
    // {
    //     return false;
    // }
    public static function getNavigationLabel(): string
    {
        return __("Withdraw methods");
    }
    public static function getPluralLabel(): ?string
    {
        return __("Withdraw methods");
    }
    public static function getModelLabel(): string
    {
        return __("Withdraw method");
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->translateLabel()
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('slug')
                    ->translateLabel()
                    ->required()
                    ->disabledOn('edit')
                    ->maxLength(255),
                Forms\Components\TextInput::make('commession')
                    ->translateLabel()
                    ->hint('لإلغاء العمولة أدخل 0')
                    ->numeric()
                    ->required()
                    ->default(0),
                Forms\Components\Select::make('commession_type')
                    ->translateLabel()
                    ->default(CommessionType::FIXED->value)
                    ->options(CommessionType::getAsOptions()),
                Forms\Components\TextInput::make('min_amount')
                    ->translateLabel()
                    ->numeric()
                    ->label(__("Minimum withdraw value")),
                Forms\Components\TextInput::make('max_amount')
                    ->translateLabel()
                    ->numeric()
                    ->label(__("Maximum withdraw value")),
                Forms\Components\TextInput::make('exchange_rate')
                    ->translateLabel()
                    ->numeric()
                    ->label('Exchange')
                    ->columnSpanFull(),
                Forms\Components\Toggle::make('is_active')
                    ->translateLabel()
                    ->required(),

            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->translateLabel()
                    ->searchable(),
                Tables\Columns\ToggleColumn::make('is_active')
                    ->translateLabel(),
                Tables\Columns\TextColumn::make('commession')
                    ->translateLabel(),
                Tables\Columns\TextColumn::make('commession_type')
                    ->translateLabel()
                    ->formatStateUsing(fn($state) => __(ucwords($state->value))),
                Tables\Columns\TextColumn::make('min_amount')
                    ->label(__("Minimum withdraw value"))
                    ->translateLabel()
                    ->suffix('ليرة'),
                Tables\Columns\TextColumn::make('max_amount')
                    ->label(__("Maximum withdraw value"))
                    ->translateLabel()
                    ->suffix('ليرة')
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([]);
    }

    public static function getRelations(): array
    {
        return [
            WithdrawInfosRelationManager::class
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListWithdrawMethods::route('/'),
            'create' => Pages\CreateWithdrawMethod::route('/create'),
            'edit' => Pages\EditWithdrawMethod::route('/{record}/edit'),
        ];
    }
}
