<?php

namespace App\Filament\Resources\UserResource\Pages;

use App\Filament\Resources\UserResource;
use App\Models\User;
use App\Models\UserBalance;
use Filament\Actions;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ListRecords;

class ListUsers extends ListRecords
{
    protected static string $resource = UserResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
            Actions\Action::make('ref_percent')
                ->label(__("Set all users ref percent"))
                ->color('danger')
                ->form([
                    TextInput::make('ref_percent')
                        ->numeric()
                        ->required()
                        ->translateLabel()
                ])->action(fn($data) => User::query()->update(['ref_percent' => $data['ref_percent']])),
            Actions\Action::make('zero_inactive_users')
                ->label(__("Set all inactive users balance to zero"))
                ->color('danger')
                ->form([
                    TextInput::make('max')
                        ->label('Max balance')
                        ->numeric()
                        ->translateLabel()
                        ->required()
                ])
                ->action(
                    function ($data) {
                        $users = User::query()
                            ->whereDoesntHave('ichancyAccount')
                            ->whereHas('userBalance', fn($query) => $query
                                ->where('active', '<=', $data['max']))
                            ->pluck('id');
                        UserBalance::whereIn('user_id', $users->toArray())
                            ->update(['active' => 0]);
                        Notification::make()
                            ->title(__(":count has been set to zero", ['count' => $users->count()]))
                            ->success()
                            ->send();
                    }
                ),
        ];
    }
}
