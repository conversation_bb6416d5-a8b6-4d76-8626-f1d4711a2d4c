<?php

namespace App\Filament\Resources\UserResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ReferralsRelationManager extends RelationManager
{
    protected static string $relationship = 'referrals';

    protected static bool $isLazy = false;
    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->required()
                    ->maxLength(255),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->translateLabel(),
                Tables\Columns\TextColumn::make('telegram_id')
                    ->searchable()
                    ->translateLabel()
                    ->copyable(),
                Tables\Columns\TextColumn::make('ichancyAccount.username')
                    ->label(__("username"))
                    ->translateLabel()
                    ->searchable()
                    ->copyable(),
                Tables\Columns\TextColumn::make('userBalance.active')
                    ->translateLabel()
                    ->prefix('ليرة')
                    ->label(__("User balance in bot"))
                    ->formatStateUsing(fn($state) => $state ?? '0')
            ])
            ->filters([
                Tables\Filters\Filter::make('active')
                    ->translateLabel()
                    ->query(fn($query) => $query->whereHas('ichancyAccount'))
            ])
            ->headerActions([
                Tables\Actions\AssociateAction::make()
                    ->preloadRecordSelect()
                    ->recordSelectSearchColumns(['name', 'telegram_id'])
                    ->multiple(),
            ])
            ->actions([

            ])
            ->bulkActions([]);
    }
}
