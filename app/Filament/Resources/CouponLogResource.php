<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CouponLogResource\Pages;
use App\Filament\Resources\CouponLogResource\RelationManagers;
use App\Models\CouponLog;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class CouponLogResource extends Resource
{
    protected static ?string $model = CouponLog::class;

    protected static ?string $navigationIcon = 'heroicon-o-clipboard-document-list';

    public static function getNavigationLabel(): string
    {
        return __('Coupon logs');
    }
    public static function getModelLabel(): string
    {
        return __("Coupon log");
    }
    public static function getPluralLabel(): ?string
    {
        return __("Coupon logs");
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('user_id')
                    ->label('User')
                    ->translateLabel()
                    ->required()
                    ->relationship('user', 'name')
                    ->preload()
                    ->searchable(),
                Forms\Components\Select::make('coupon_id')
                    ->required()
                    ->label('Coupon')
                    ->translateLabel()
                    ->relationship('coupon', 'code')
                    ->preload()
                    ->searchable(),
                Forms\Components\DatePicker::make('created_at')
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('created_at', 'desc')
            ->modifyQueryUsing(fn($query) => $query->with('user.ichancyAccount', 'coupon'))
            ->columns([
                Tables\Columns\TextColumn::make('user.telegram_id')
                    ->label(__("Telegram id"))
                    ->translateLabel()
                    ->copyable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('user.name')
                    ->translateLabel()
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('user.ichancyAccount.username')
                    ->label(__("Ichancy account"))
                    ->translateLabel()
                    ->copyable()
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('coupon.code')
                    ->translateLabel()
                    ->searchable()
                    ->copyable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('coupon.value')
                    ->label(__("Value"))
                    ->translateLabel()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->translateLabel()
                    ->date('Y-m-d')
                    ->sortable(),
            ])
            ->filters([
                Filter::make('created_at')
                    ->form([
                        Forms\Components\DatePicker::make('created_at'),
                    ])->query(fn($data, $query) => $query->when(isset($data['created_at']), fn($query) => $query->whereDate('created_at', $data['created_at'])))
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    // Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCouponLogs::route('/'),
            'create' => Pages\CreateCouponLog::route('/create'),
            'edit' => Pages\EditCouponLog::route('/{record}/edit'),
        ];
    }
}
