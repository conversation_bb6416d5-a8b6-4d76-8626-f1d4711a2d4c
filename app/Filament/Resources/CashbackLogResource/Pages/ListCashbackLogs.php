<?php

namespace App\Filament\Resources\CashbackLogResource\Pages;

use App\Filament\Resources\CashbackLogResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListCashbackLogs extends ListRecords
{
    protected static string $resource = CashbackLogResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make(),
        ];
    }
}
