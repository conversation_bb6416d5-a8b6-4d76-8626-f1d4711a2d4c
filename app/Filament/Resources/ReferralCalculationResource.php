<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ReferralCalculationResource\Pages;
use App\Filament\Resources\ReferralCalculationResource\RelationManagers;
use App\Filament\Resources\ReferralCalculationResource\RelationManagers\ItemsRelationManager;
use App\Models\ReferralCalculation;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ReferralCalculationResource extends Resource
{
    protected static ?string $model = ReferralCalculation::class;

    protected static ?string $navigationIcon = 'heroicon-o-link';
    // public static function shouldRegisterNavigation(): bool
    // {
    //     return false;
    // }
    public static function getNavigationLabel(): string
    {
        return __("Referrals");
    }
    public static function getPluralLabel(): ?string
    {
        return __("Referrals");
    }
    public static function getModelLabel(): string
    {
        return __("Referral");
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('total_referrals')
                    ->required()
                    ->numeric()
                    ->default(0),
                Forms\Components\TextInput::make('total_active_referrals')
                    ->required()
                    ->numeric()
                    ->default(0),
                Forms\Components\TextInput::make('total_profit')
                    ->required()
                    ->numeric()
                    ->default(0),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('created_at', 'desc')
            ->columns([
                Tables\Columns\TextColumn::make('total_referrals')
                    ->translateLabel()
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('total_active_referrals')
                    ->translateLabel()
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('total_profit')
                    ->translateLabel()
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->translateLabel()
                    ->dateTime()
                    ->sortable()
                    ->toggleable()
            ])
            ->filters([
                //
            ])
            ->actions([])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            ItemsRelationManager::make()
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListReferralCalculations::route('/'),
            'view' => Pages\ViewReferralCalculation::route('/{record}'),
            'create' => Pages\CreateReferralCalculation::route('/create'),
        ];
    }
}
