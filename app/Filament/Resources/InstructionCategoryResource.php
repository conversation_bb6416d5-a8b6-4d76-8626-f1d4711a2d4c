<?php

namespace App\Filament\Resources;

use App\Filament\Resources\InstructionCategoryResource\Pages;
use App\Filament\Resources\InstructionCategoryResource\RelationManagers;
use App\Filament\Resources\InstructionCategoryResource\RelationManagers\InstructionsRelationManager;
use App\Models\InstructionCategory;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class InstructionCategoryResource extends Resource
{
    protected static ?string $model = InstructionCategory::class;

    protected static ?string $navigationIcon = 'heroicon-o-information-circle';

    public static function getNavigationLabel(): string
    {
        return __('Instruction categories');
    }
    public static function getModelLabel(): string
    {
        return __("Instruction categories");
    }
    public static function getPluralLabel(): ?string
    {
        return __("Instruction categories");
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->translateLabel()
                    ->required()
                    ->maxLength(255),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            InstructionsRelationManager::class
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListInstructionCategories::route('/'),
            'create' => Pages\CreateInstructionCategory::route('/create'),
            'edit' => Pages\EditInstructionCategory::route('/{record}/edit'),
        ];
    }
}
