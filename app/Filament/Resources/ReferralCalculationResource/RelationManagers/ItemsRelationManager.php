<?php

namespace App\Filament\Resources\ReferralCalculationResource\RelationManagers;

use App\Facades\Bot;
use App\Facades\BotMessage;
use App\Facades\Settings;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ItemsRelationManager extends RelationManager
{
    protected static string $relationship = 'items';
    protected static bool $isLazy = false;

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('user_id')
                    ->required()
                    ->maxLength(255),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('user_id')
            ->columns([
                Tables\Columns\TextColumn::make('user.name')
                    ->translateLabel()
                    ->searchable(),
                Tables\Columns\TextColumn::make('user.telegram_id')
                    ->translateLabel()
                    ->searchable()
                    ->copyable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('user.ref_percent')
                    ->label('Ref percent')
                    ->translateLabel()
                    ->numeric()
                    ->prefix('%'),
                Tables\Columns\TextColumn::make('total_referrals')
                    ->sortable()
                    ->translateLabel(),
                Tables\Columns\TextColumn::make('total_active_referrals')
                    ->translateLabel()
                    ->sortable(),
                Tables\Columns\TextColumn::make('total_profit')
                    ->translateLabel()
                    ->sortable()
            ])
            ->filters([
                //
            ])
            ->headerActions([])
            ->actions([
                Tables\Actions\Action::make('send_profit')
                    ->translateLabel()
                    ->action(function ($record) {
                        static::sendProfit($record);
                        Notification::make()
                            ->title(__("Success"))
                            ->success()
                            ->send();
                    })->hidden(fn($record) => $record?->sent_profit),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([]),
            ]);
    }
    public static function sendProfit($item)
    {
        if (!$item || $item->sent_profit) return;
        $balance = $item->user->userBalance;
        $balance->active += $item->total_profit;
        $balance->save();
        $item->sent_profit = true;
        $item->save();

        $message = BotMessage::setChat($item->user->telegram_id)
            ->setText('تم إضافة أرباح الإحالات الى رصيدك في البوت, المبلغ: ' . $item->total_profit . ' ليرة');
        Bot::sendMessage($message->toArray());
    }
}
