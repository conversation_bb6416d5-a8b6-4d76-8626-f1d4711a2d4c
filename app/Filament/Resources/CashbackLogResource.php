<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CashbackLogResource\Pages;
use App\Filament\Resources\CashbackLogResource\RelationManagers;
use App\Models\CashbackLog;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class CashbackLogResource extends Resource
{
    protected static ?string $model = CashbackLog::class;

    protected static ?string $navigationIcon = 'heroicon-o-banknotes';
    public static function shouldRegisterNavigation(): bool
    {
        return false;
    }
    public static function getNavigationLabel(): string
    {
        return __('Cashback Logs');
    }
    public static function getModelLabel(): string
    {
        return __('Cashback Log');
    }
    public static function getPluralLabel(): string
    {
        return __('Cashback Logs');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('user_id')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('payment_method_id')
                    ->numeric(),
                Forms\Components\TextInput::make('balance')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('cashback')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('type')
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('user.name')
                    ->translateLabel()
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('user.telegram_id')
                    ->label(__("Telegram id"))
                    ->translateLabel()
                    ->copyable()
                    ->searchable()
                    ->sortable(),
                // Tables\Columns\TextColumn::make('balance')
                //     ->translateLabel()
                //     ->numeric()
                //     ->sortable(),
                Tables\Columns\TextColumn::make('cashback')
                    ->translateLabel()
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('paymentMethod.name')
                    ->translateLabel()
                    ->label(__('Payment Method'))
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->translateLabel()
                    ->dateTime('Y-m-d H:i')
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCashbackLogs::route('/'),
            'create' => Pages\CreateCashbackLog::route('/create'),
            'edit' => Pages\EditCashbackLog::route('/{record}/edit'),
        ];
    }
}
