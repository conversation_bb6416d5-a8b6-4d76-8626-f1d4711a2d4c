<?php

namespace App\Filament\Widgets;

use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Forms;
use Filament\Widgets\Widget;

class FilterPaymentsWidget extends Widget implements HasForms
{
    use InteractsWithForms;
    protected static string $view = 'filament.widgets.filter-payments-widget';
    protected static ?int $sort = 1;
    protected int | string | array $columnSpan = 'full';
    public ?array $data = [];

    public function mount()
    {
        $this->form->fill(['time' => 'today']);
    }
    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('time')
                    ->translateLabel()
                    ->options([
                        'today' => __("Today"),
                        'yesterday' => __("Yesterday"),
                        'this_week' => __("This week"),
                        'this_month' => __("This month"),
                    ])->columnSpanFull()
                    ->live()
                    ->afterStateUpdated(fn($state) => $this->dispatch('updateFilter', $state))
            ])
            ->statePath('data');
    }
}
