<?php

namespace App\Filament\Widgets;

use App\Models\ChargeRequest;
use App\Models\User;
use App\Models\UserBalance;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Number;

class UserStat extends BaseWidget
{
    protected static ?int $sort = 3;
    protected function getStats(): array
    {
        return [
            Stat::make(__("Registered users today"), User::whereDate('created_at', now())->count('id')),
            Stat::make(__("Total Users"), User::count('id')),
            Stat::make(__("Total balance"), Number::format(UserBalance::sum('active')) . ' ليرة')
        ];
    }
}
