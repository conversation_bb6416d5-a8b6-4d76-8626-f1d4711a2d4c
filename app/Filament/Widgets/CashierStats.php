<?php

namespace App\Filament\Widgets;

use App\Facades\Cashier;
use App\Models\CouponLog;
use App\Models\UserBalance;
use App\Services\ChargeRequestService;
use App\Services\CouponLogService;
use App\Services\DailyLogService;
use App\Services\UserService;
use App\Services\WithdrawRequestService;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Number;
use Livewire\Attributes\On;

class CashierStats extends BaseWidget
{
    protected static ?int $sort = 2;
    protected static ?string $pollingInterval = null;
    protected ChargeRequestService $chargeRequestService;
    protected WithdrawRequestService $withdrawRequestService;
    protected CouponLogService $couponLogService;
    protected DailyLogService $dailyLogService;

    protected array $filters = [
        'time' => 'today'
    ];

    public function boot(
        ChargeRequestService $chargeRequestSErvice,
        WithdrawRequestService $withdrawRequestService,
        CouponLogService $couponLogService,
        DailyLogService $dailyLogService
    ) {
        $this->dailyLogService = $dailyLogService;
        $this->chargeRequestService = $chargeRequestSErvice;
        $this->withdrawRequestService = $withdrawRequestService;
        $this->couponLogService = $couponLogService;
    }

    protected function getStats(): array
    {
        $totalCharge = $this->chargeRequestService->getTotalChargeAmount($this->filters);
        $totalWithdraw = $this->withdrawRequestService->getTotalWithdraw($this->filters);
        $couponLogs = $this->couponLogService->getLogs($this->filters);
        $totalBalance = UserBalance::sum('active');
        return [
            Stat::make(__("Total charge amount in the bot"),  Number::format($totalCharge) . ' ليرة'),
            Stat::make(__("Total withdraw amount from the bot"), Number::format($totalWithdraw) . ' ليرة'),
            Stat::make(__("Total Burned amount"), Number::format((floatval($totalCharge) - floatval($totalWithdraw) - floatval($totalBalance))) . 'ليرة'),
            Stat::make(__("Total coupons used"), Number::format($couponLogs) . ' ليرة')
        ];
    }
    #[On('updateFilter')]
    public function updateFilter($time)
    {
        $this->filters['time'] = $time;
    }
}
