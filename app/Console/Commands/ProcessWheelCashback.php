<?php

namespace App\Console\Commands;

use App\Facades\Bot;
use App\Facades\BotMessage;
use App\Helpers\ChargeRequestStatus;
use App\Models\ChargeRequest;
use App\Models\GiftLog;
use App\Models\WheelCashbackLog;
use App\Models\WithdrawRequest;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ProcessWheelCashback extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:process-wheel-cashback';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process wheel cashback prizes and add them to user balances';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Processing wheel cashback prizes...');

        try {
            // Get all unprocessed cashback logs
            $unprocessedLogs = WheelCashbackLog::where('is_processed', false)
                ->with(['user', 'wheelPrize', 'wheelPrize.wheelPrizeType'])
                ->get();

            $this->info("Found {$unprocessedLogs->count()} unprocessed cashback prizes.");

            if ($unprocessedLogs->isEmpty()) {
                $this->info('No unprocessed cashback prizes found.');
                return 0;
            }

            // Process each log
            $processedCount = 0;
            $failedCount = 0;

            foreach ($unprocessedLogs as $log) {
                $this->info("Processing cashback prize for user ID: {$log->user_id}");

                try {
                    DB::beginTransaction();

                    // Calculate the base amount for cashback
                    $totalChargeAmount = ChargeRequest::where('user_id', $log->user_id)
                        ->where('status', ChargeRequestStatus::APPROVED)
                        ->sum('amount');

                    $totalWithdrawAmount = WithdrawRequest::where('user_id', $log->user_id)
                        ->where('status', 'approved')
                        ->sum('amount');

                    $totalGiftedAmount = GiftLog::where('sender_id', $log->user_id)
                        ->sum('amount');

                    $userBalance = $log->user->userBalance()->firstOrCreate();
                    $currentBalance = $userBalance->active;

                    // Calculate the base amount for cashback
                    // total charge amount - (user balance + total withdraw amount - total amount gifted to other users)
                    $baseAmount = $totalChargeAmount - ($currentBalance + $totalWithdrawAmount - $totalGiftedAmount);

                    // Ensure base amount is not negative
                    $baseAmount = max(0, $baseAmount);

                    // Calculate cashback value based on the prize percentage
                    $cashbackAmount = $baseAmount * ($log->percentage / 100);

                    // Update the log with calculated values
                    $log->base_amount = $baseAmount;
                    $log->cashback_amount = $cashbackAmount;

                    // Add the cashback to the user's balance
                    $userBalance->active += $cashbackAmount;
                    $userBalance->save();

                    // Mark the log as processed
                    $log->is_processed = true;
                    $log->processed_at = now();
                    $log->save();

                    // Send notification to the user
                    if ($cashbackAmount > 0) {
                        $message = BotMessage::setChat($log->user->telegram_id)
                            ->setText("
تم إضافة كاش باك بقيمة " . number_format($cashbackAmount) . " ليرة إلى رصيدك!
نسبة الكاش باك: {$log->percentage}%
المبلغ الأساسي: " . number_format($baseAmount) . " ليرة
رصيدك الحالي: " . number_format($userBalance->active) . " ليرة
                            ")->setParseMode('HTML');

                        Bot::sendMessage($message->toArray());
                    }

                    DB::commit();
                    $processedCount++;

                    $this->info("Successfully processed cashback prize for user ID: {$log->user_id}. Amount: {$cashbackAmount}");
                } catch (\Exception $e) {
                    DB::rollBack();
                    $failedCount++;

                    $this->error("Failed to process cashback prize for user ID: {$log->user_id}. Error: {$e->getMessage()}");
                    Log::error("Failed to process wheel cashback prize", [
                        'user_id' => $log->user_id,
                        'log_id' => $log->id,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                }
            }

            $this->info("Processed {$processedCount} cashback prizes successfully.");

            if ($failedCount > 0) {
                $this->warn("Failed to process {$failedCount} cashback prizes.");
            }

            // Send a summary to the notification group
            $notificationGroup = config('ichancy.groups.reports_group');
            if ($notificationGroup) {
                $message = BotMessage::setChat($notificationGroup)
                    ->setText("
تقرير معالجة جوائز الكاش باك:
عدد الجوائز المعالجة: {$processedCount}
عدد الجوائز التي فشلت المعالجة: {$failedCount}
                    ")->setParseMode('HTML');

                Bot::sendMessage($message->toArray());
            }

            return 0;
        } catch (\Exception $e) {
            $this->error("An error occurred while processing wheel cashback prizes: {$e->getMessage()}");
            Log::error("An error occurred while processing wheel cashback prizes", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return 1;
        }
    }
}
