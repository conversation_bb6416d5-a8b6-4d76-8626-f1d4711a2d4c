<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class IsValidTelegramRequest
{
    public function handle(Request $request, Closure $next): Response
    {
        Log::info($request->all());
        $id = $request->input('id');
        if (!$id) {
            return response(403);
        }

        return $next($request);
    }
}
