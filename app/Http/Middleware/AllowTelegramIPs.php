<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class AllowTelegramIPs
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $initData = $request->header('X-Telegram-Init-Data') ?? $request->input('initData');

        if (!$initData || !$this->verifyTelegramInitData($initData, env('TELEGRAM_BOT_TOKEN'))) {
            abort(403);
        }

        return $next($request);
    }
    protected function verifyTelegramInitData(string $initData, string $botToken): bool
    {
        parse_str($initData, $data);
        $hash = $data['hash'] ?? null;
        unset($data['hash']);

        ksort($data);
        $checkString = '';
        foreach ($data as $key => $value) {
            $checkString .= "$key=$value\n";
        }
        $checkString = rtrim($checkString, "\n");

        $secretKey = hash('sha256', $botToken, true);
        $calculatedHash = hash_hmac('sha256', $checkString, $secretKey);

        return hash_equals($calculatedHash, $hash);
    }
}
