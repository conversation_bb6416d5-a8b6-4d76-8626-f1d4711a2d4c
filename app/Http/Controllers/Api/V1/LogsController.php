<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Services\DailyLogService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class LogsController extends Controller
{
    public function __construct(protected DailyLogService $dailyLogService)
    {
    }

    /**
     * Get the daily report for a specific date and send it to the Telegram group
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $request->validate(['date' => ['required', 'date']]);

        // Convert the date string to a Carbon instance
        $date = Carbon::parse($request->date);

        try {
            // Use the DailyLogService to generate and send the report to Telegram
            $telegramResponse = $this->dailyLogService->dailyLog($date);

            return response()->json([
                'success' => true,
                'telegram_sent' => $telegramResponse !== null,
                'message' => 'Daily report sent to Telegram successfully'
            ]);
        } catch (\Throwable $e) {
            // Log the error but don't fail the API request
            Log::error('Failed to send report to Telegram: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'telegram_sent' => false,
                'message' => 'Failed to send daily report to Telegram',
                'error' => $e->getMessage()
            ], 500);
        }
    }


}
