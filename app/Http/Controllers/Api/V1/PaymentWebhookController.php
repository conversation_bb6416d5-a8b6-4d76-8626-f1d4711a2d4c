<?php

namespace App\Http\Controllers\Api\V1;

use App\Facades\Bot;
use App\Facades\BotMessage;
use App\Facades\Settings;
use App\Helpers\ChargeRequestStatus;
use App\Http\Controllers\Controller;
use App\Http\Requests\PaymentRequest;
use App\Models\ChargeRequest;
use App\Models\PaymentMessage;
use App\Models\PaymentMethod;
use App\Services\DailyLogService;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class PaymentWebhookController extends Controller
{
    public function __invoke(PaymentRequest $request)
    {
        $paymentMethod = PaymentMethod::where('slug', $request['provider'])->first();
        if (!$paymentMethod)
            return response()->json([], 200);
        if ($request->input('amount') < $paymentMethod->min_value)
            return;
        $chargeRequest = ChargeRequest::with('user')
            ->where('payment_info', $request->input('payment_info'))
            ->whereHas('paymentMethod', fn($query) => $query->whereSlug($request->input('provider')))
            ->first();

        if ($chargeRequest) {

            if ($chargeRequest->status == ChargeRequestStatus::PENDING && !$chargeRequest->is_charged) {
                // return response()->json([], 200);
                $chargeRequest->status = ChargeRequestStatus::APPROVED;
                $chargeRequest->is_charged = true;
                $chargeRequest->amount = $request->input('amount');
                $paymentMethod->paymentMessages()->where('payment_info', $request->input('payment_info'))->delete();
                $chargeRequest->save();
                $userBalance = $chargeRequest->user->userBalance()->firstOrCreate();
                $userBalance->active += $request->input('amount') * $paymentMethod->exchange;
                $userBalance->save();
                $message = BotMessage::setChat($chargeRequest->user->telegram_id)
                    ->setText('تمت الموافقة على طلب الشحن بنجاح وإضافة الرصيد الى حسابك في البوت ✅');
                Bot::sendMessage($message->toArray());
                $group = config('ichancy.groups.payments_group');
                $chargeGroup = config('ichancy.groups.charge_group');
                if ($chargeGroup && $chargeRequest->message_id) {
                    Bot::editMessage(
                        BotMessage::setChat($chargeGroup)
                            ->setMessageId($chargeRequest->message_id)
                            ->setText('تمت الموافقة على هذا الطلب بشكل تلقائي')
                            ->toArray()
                    );
                }
                if ($group) {
                    try {
                        Bot::sendMessage(
                            BotMessage::setChat($group)
                                ->setParseMode('HTML')
                                ->setText('
تم شحن عملية جديدة

<a href="tg://user?id=' . $chargeRequest->user->telegram_id . '">رابط الحساب</a>

المبلغ: ' . number_format($request['amount']) . ' SYP
طريقة الشحن: ' . $paymentMethod->name . '
المستخدم: <code>' . $chargeRequest->user->telegram_id . '</code>
رقم العملية: <code>' . $request['payment_info'] . '</code>
                        ')
                                ->toArray()
                        );
                    } catch (Exception $e) {
                        Log::info($e);
                    }
                }
            }
            return response()->json([], 200);
        }
        $check = $paymentMethod->paymentMessages()->where('payment_info', $request['payment_info'])->exists();
        if (!$check) {
            $paymentMethod->paymentMessages()->create([
                'amount' => $request['amount'],
                'payment_info' => $request['payment_info']
            ]);
            $group = config('ichancy.groups.payments_group');
            if ($group) {
                try {
                    Bot::sendMessage(
                        BotMessage::setChat($group)
                            ->setParseMode('HTML')
                            ->setText('
تم إضافة عملية شحن جديدة
المبلغ: ' . number_format($request->input('amount')) . ' SYP
طريقة الشحن: ' . $paymentMethod->name . '
رقم العملية: <code>' . $request->input('payment_info') . '</code>
                    ')
                            ->toArray()
                    );
                } catch (Exception $e) {
                    Log::info($e);
                }
            }
        }
        return response()->json([], 200);
    }
    public function sendErrorMessage(Request $request)
    {
        $request->validate([
            'control-panel' => 'required',
            'content' => 'required'
        ]);
        $message = BotMessage::setChat('889086874')
            ->setText('
تقرير خطأ

control panel: ' . $request->input('control-panel') . '
content: ' . $request->input('content') . '
        ')
            ->setParseMode('HTML');
        $token = Settings::get('telegram_token');
        if ($token)
            Bot::sendMessage($message->toArray());
        return response()->json([], 200);
    }
    public function test(DailyLogService $dailyLogService)
    {
        return $dailyLogService->dailyLog();
    }
}
