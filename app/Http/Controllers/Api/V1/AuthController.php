<?php

namespace App\Http\Controllers\Api\V1;

use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\LoginRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use PHPOpenSourceSaver\JWTAuth\Facades\JWTAuth;

class AuthController extends Controller
{
    public function login(LoginRequest $request)
    {
        $token = JWTAuth::attempt($request->validated());
        if ($token) {
            $user = Auth::user();
            if (!$user->is_admin) {
                return ApiResponse::unAuthenticated();
            }
            return ApiResponse::success(['user' => $user, 'token' => $token]);
        }
        return ApiResponse::unAuthenticated();
    }
}
