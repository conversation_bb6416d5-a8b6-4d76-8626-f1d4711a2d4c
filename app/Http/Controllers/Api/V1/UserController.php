<?php

namespace App\Http\Controllers\Api\V1;

use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Services\UserService;
use Illuminate\Http\Request;

class UserController extends Controller
{
    public function __construct(protected UserService $userService) {}
    public function index(Request $request)
    {

        $users = $this->userService->all(50, ['userBalance', 'ichancyAccount']);
        return ApiResponse::success($users);
    }
}
