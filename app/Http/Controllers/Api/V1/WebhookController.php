<?php

namespace App\Http\Controllers\Api\V1;

use App\BotQueries\Admin\BroadcastQuery;
use App\BotQueries\Admin\Charge\ConfirmChargeQuery;
use App\BotQueries\Admin\Charge\RejectChargeQuery;
use App\BotQueries\Admin\ConfirmSendmoneyQuery;
use App\BotQueries\Admin\MessageQuery as AdminMessageQuery;
use App\BotQueries\Admin\ReplyToSupportMessageQuery;
use App\BotQueries\Admin\Supprt\CancelReplyToMessageQuery;
use App\BotQueries\Admin\Supprt\ConfirmReplyToMessageQuery;
use App\BotQueries\Admin\Withdraw\ConfirmWithdrawQuery;
use App\BotQueries\Admin\Withdraw\RejectWithdrawQuery;
use App\BotQueries\CancelWithdrawRequestQuery;
use App\BotQueries\ChargeQuery;
use App\BotQueries\ChargeRequestQuery;
use App\BotQueries\ConfirmWithdrawRequestQuery;
use App\BotQueries\Ichancy\ChargeIchancyQuery;
use App\BotQueries\Ichancy\WithdrawIchancyQuery;
use App\BotQueries\IchancyQuery;
use App\BotQueries\MessageQuery;
use App\BotQueries\MyAccountQuery;
use App\BotQueries\Referral\MyReferralLinkQuery;
use App\BotQueries\Referral\MyReferralsQuery;
use App\BotQueries\Referral\ReferralMessageQuery;
use App\BotQueries\Referral\ReferralQuery;
use App\BotQueries\RegisterQuery;
use App\BotQueries\StartQuery;
use App\BotQueries\SupportMessages\CancelSupportMessageQuery;
use App\BotQueries\SupportMessages\ConfirmSupportMessageQuery;
use App\BotQueries\SupportMessages\SupportMessageQuery;
use App\BotQueries\WithdrawQuery;
use App\BotQueries\WithdrawRequestQuery;
use App\Facades\Bot;
use App\Facades\BotMessage;
use App\Facades\Settings;
use App\Http\Controllers\Controller;
use App\Services\PaymentMethodService;
use App\Services\WithdrawMethodService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class WebhookController extends Controller
{
    public function __construct(
        protected PaymentMethodService $paymentMethodService,
        protected WithdrawMethodService $withdrawMethodService
    ) {}
    public function __invoke(
        string $token,
        Request $request,
    ) {
        Bot::setToken($token);
        $message = $request['message'];
        $command = $request['callback_query'];
        if (isset($message['from'])) {
            $this->checkReferral($message);
            $check = $this->checkChatMember($message['from']['id']);
            if (!$check) {

                return response()->json([], 200);
            }
        }
        if (isset($command['message']['chat']['type']) && in_array($command['message']['chat']['type'], ['group', 'supergroup'])) {
            // here you handle group commands and messages
            $this->handleGroupQuery($command);
            return response()->json([], 200);
        }

        if (isset($message['entities'][0]['type']) && $message['entities'][0]['type'] == 'bot_command') {
            Bot::handleCommands();
        } elseif (isset($command['data'])) {
            $this->handleCallbackQuery($command, $token);
        } elseif ($message) {
            if (in_array($message['chat']['type'], ['group', 'supergroup'])) {
                AdminMessageQuery::handle($message);
            } else {
                MessageQuery::handle($message);
            }
        }

        return response()->json([], 200);
    }
    protected function handleCallbackQuery($query)
    {
        if (str_starts_with($query['data'], 'payment_method')) {
            $this->handlePaymentMethodInfo($query);
            return;
        }
        if (str_starts_with($query['data'], 'withdraw_method')) {
            $this->handleWithdrawMethodInfo($query);
            return;
        }
        if (str_starts_with($query['data'], 'confirm_withdraw_request')) {
            ConfirmWithdrawRequestQuery::handle($query);
            return;
        }
        if (str_starts_with($query['data'], 'cancel_withdraw_request')) {
            CancelWithdrawRequestQuery::handle($query);
            return;
        }
        if (str_starts_with($query['data'], 'confirm_support_message_')) {
            ConfirmSupportMessageQuery::handle($query);
            return;
        }
        if (str_starts_with($query['data'], 'cancel_support_message_')) {
            CancelSupportMessageQuery::handle($query);
            return;
        }
        switch ($query['data']) {
            case 'charge':
                ChargeQuery::handle($query);
                break;
            case 'withdraw':
                WithdrawQuery::handle($query);
                break;
            case 'ichancy':
                IchancyQuery::handle($query);
                break;
            case 'register':
                RegisterQuery::handle($query);
                break;
            case 'my_account':
                MyAccountQuery::handle($query);
                break;
            case 'ichancy_charge':
                ChargeIchancyQuery::handle($query);
                break;
            case 'ichancy_withdraw':
                WithdrawIchancyQuery::handle($query);
                break;
            case 'back_to_start':
                StartQuery::handle($query);
                break;
            case 'referral':
                ReferralQuery::handle($query);
                break;
            case 'referral_link':
                MyReferralLinkQuery::handle($query);
                break;
            case 'referral_description':
                ReferralMessageQuery::handle($query);
                break;
            case 'my_referrals':
                MyReferralsQuery::handle($query);
                break;
            case 'support':
                SupportMessageQuery::handle($query);
                break;
        }
    }
    protected function handleWithdrawMethodInfo($query)
    {
        $parts = explode('_', $query['data']);
        if (!isset($parts[count($parts) - 1])) return;

        $withdrawMethod = $this->withdrawMethodService->show($parts[count($parts) - 1], ['withdrawInfos']);
        if (!$withdrawMethod) return;
        WithdrawRequestQuery::handle([
            'withdraw_method' => $withdrawMethod,
            'query' => $query
        ]);
    }
    protected function handlePaymentMethodInfo($query)
    {
        $parts = explode('_', $query['data']);
        if (!isset($parts[count($parts) - 1])) return;

        $paymentMethod = $this->paymentMethodService->show($parts[count($parts) - 1], ['paymentInfos']);
        if (!$paymentMethod) return;
        ChargeRequestQuery::handle([
            'payment_method' => $paymentMethod,
            'query' => $query
        ]);
    }
    protected function handleGroupQuery($query)
    {
        if (str_starts_with($query['data'], 'confirm_withdraw_request')) {
            ConfirmWithdrawQuery::handle($query);
            return;
        }
        if (str_starts_with($query['data'], 'reject_withdraw_request')) {
            RejectWithdrawQuery::handle($query);
            return;
        }
        if (str_starts_with($query['data'], 'confirm_charge_request')) {
            ConfirmChargeQuery::handle($query);
            return;
        }
        if (str_starts_with($query['data'], 'reject_charge_request')) {
            RejectChargeQuery::handle($query);
            return;
        }
        if (str_starts_with($query['data'], 'confirm_sendmoney')) {
            ConfirmSendmoneyQuery::handle($query);
            return;
        }
        if (str_starts_with($query['data'], 'reply_support_message_')) {
            ReplyToSupportMessageQuery::handle($query);
            return;
        }
        if (str_starts_with($query['data'], 'confirm_admin_reply_support_message_')) {
            ConfirmReplyToMessageQuery::handle($query);
            return;
        }
        if (str_starts_with($query['data'], 'cancel_admin_reply_support_message_')) {
            CancelReplyToMessageQuery::handle($query);
            return;
        }
        switch ($query['data']) {
            case 'broadcast':
                BroadcastQuery::handle($query);
                break;
        }
    }
    protected function checkChatMember(string $userId)
    {
        $admin_channel = Settings::get('admin_channel');
        if ($admin_channel) {
            $check = Bot::getChatMember(str_starts_with($admin_channel, '@') ? $admin_channel : '@' . $admin_channel, $userId);
            if (isset($check['result']['status']) && $check['result']['status'] != 'left') {
                return true;
            }
            $text = Settings::get('channel_message');
            if (!$text) {
                $text = 'أهلاً وسهلاً بك 
يرجى الاشتراك بالقناة اولاً لتتمكن من استخدام البوت';
            }
            Bot::sendMessage(BotMessage::setChat($userId)
                ->setText($text)
                ->addButton(text: 'قناة البوت', url: 'https://t.me/' . $admin_channel)
                ->toArray());
            return false;
        }
        return true;
    }
    public function checkReferral($message)
    {
        if (!isset($message['text'])) return;
        $parts = explode(' ', $message['text']);
        if (!isset($parts[1])) return;
        $referral = $parts[1];

        Cache::forever('referral_' . $message['from']['id'], $referral);
    }
    public function test(Request $request) {}
}
