<?php

namespace App\Livewire;

use App\Facades\Bot;
use App\Facades\BotMessage;
use App\Facades\Settings;
use App\Helpers\ChargeRequestStatus;
use App\Models\ChargeRequest;
use App\Models\Setting;
use App\Models\User;
use App\Models\WheelCashbackLog;
use App\Models\WheelLog;
use App\Models\WheelPrize;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Number;
use Livewire\Component;

class Wheel extends Component
{
    public $prizes;
    public bool $showDialog = false;
    public $prize = null;
    public $user;
    public bool $canSpin = false;
    public $spins = 1;
    public string $background = '';
    public ?string $stroke;
    public string $btn;
    public string $pin;
    public ?string $primaryColor;
    public ?string $secondaryColor;
    public array $odds = [];
    public $settings;
    public function mount($id)
    {
        $user = Cache::get("wheel_user_$id", fn() => User::find($id));
        if (!$user || !$user->is_active)
            return abort(404);
        $this->pin = asset('images/pin.png');
        $this->background = asset('images/back.jpg');
        $bg = Settings::get('wheel_background');
        $btn = Settings::get('wheel_btn');
        $pin = Settings::get('wheel_pin');
        $this->primaryColor = Settings::get('wheel_main_color') ?? '#000000';
        $this->secondaryColor = Settings::get('wheel_secondary_color') ?? '#3002fc';
        $this->settings = Setting::pluck('value', 'key')->toArray();
        if ($btn) {
            $this->btn = asset('storage/' . $btn);
        }
        if ($pin) {
            $this->pin = asset('storage/' . $pin);
        }
        if ($bg) {
            $this->background = asset('storage/' . $bg);
        }
        $stroke = Settings::get('wheel_stroke');
        if ($stroke) {
            $this->stroke = asset('storage/' . $stroke);
        }
        $this->user = $user;

        $this->calculateSpins();
        $this->prizes = WheelPrize::with('wheelPrizeType')
            ->where('is_active', true)
            ->limit(8)
            ->orderBy('sort')
            ->get();

        $this->odds = $this->getOdds();
    }
    public function calculateSpins()
    {
        $minCharge = Settings::get('wheel_min_charge');
        $totalCharge = ChargeRequest::where('user_id', $this->user->id)
            ->whereDate('created_at', now())
            ->where('status', ChargeRequestStatus::APPROVED)
            ->sum('amount');
        $totalCount = $minCharge == 0 ? $minCharge : intval($totalCharge / $minCharge);
        $totalSpins = WheelLog::query()
            ->where('user_id', $this->user->id)
            ->whereHas('wheelPrize', fn($q) => $q->whereHas(
                'wheelPrizeType',
                fn($q) => $q->where('slug', '!=', 'free_spin')

            ))
            ->whereDate('created_at', now())
            ->count('id');
        if ($totalCount < $totalSpins) {
            $this->spins = 0;
        } else {

            $this->spins = intval($totalCount - $totalSpins);
        }
    }
    public function render()
    {
        return view('livewire.wheel');
    }
    public function checkPrize($index)
    {
        if ($this->spins <= 0)
            return;
        $prize = isset($this->prizes[$index]) ? $this->prizes[$index] : null;
        if (!$prize)
            return;

        $this->prize = $prize;
        WheelLog::create([
            'user_id' => $this->user->id,
            'wheel_prize_id' => $prize->id,
        ]);
        $this->calculateSpins();
        $balance = $this->user->userBalance()->firstOrCreate();
        switch ($prize->wheelPrizeType->slug) {
            case 'cash_back':
                // Instead of processing immediately, save to WheelCashbackLog for later processing
                WheelCashbackLog::create([
                    'user_id' => $this->user->id,
                    'wheel_prize_id' => $prize->id,
                    'percentage' => $prize->value,
                    'is_processed' => false,
                ]);

                // Notify the user that they won a cashback prize
                $message = BotMessage::setChat($this->user->telegram_id)
                    ->setText('
مبروك! لقد ربحت كاش باك بنسبة ' . $prize->value . '% من العجلة!
سيتم احتساب قيمة الكاش باك وإضافتها إلى رصيدك قريباً.
                    ')->setParseMode('HTML');

                Bot::sendMessage($message->toArray());
                break;
            case 'money':
                $balance->active += $prize->value;
                $balance->save();
                $this->user->load('userBalance');
                $this->sendNotificationToAdmin($this->user, $prize->value, $balance->active);
                break;
            case 'free_spin':
                break;
        }

    }
    public function getValue()
    {
        if (!$this->prize)
            return '';
        switch ($this->prize->wheelPrizeType->slug) {
            case 'cash_back':
                return $this->prize->value . '%';
            case 'free_spin':
            case 'hard_luck':
                return '';
            case 'money':
                return Number::format($this->prize->value);
            default:
                return $this->prize->value;
        }
    }
    public function getOdds()
    {
        $odds = [];
        if ($this->prizes === null)
            return [];
        foreach ($this->prizes as $prize) {
            $odd = $prize->win_percent;
            for ($i = 0; $i < $odd; $i++) {
                $odds[] = $prize->id;
            }
        }
        for ($i = 0; $i < 100 - count($odds); $i++) {
            $odds[] = 0;
        }
        shuffle($odds);
        return $odds;
    }
    public function checkDialog()
    {

        if ($this->prize) {
            if ($this->prize->wheelPrizeType->slug == 'hard_luck')
                return;
            $this->showDialog = true;
        } else {
            $this->showDialog = false;
        }
    }
    public function sendNotificationToAdmin($user, $value, $balance)
    {
        $message = BotMessage::setChat(config('ichancy.groups.notification_group'))
            ->setText('
تم اضافة مبلغ ' . number_format($value) . ' للمستخدم <code>' . $user->telegram_id . '</code>
جائزة العجلة

<a href="tg://user?id=' . $user->telegram_id . '">رابط الحساب</a>

رصيد المستخدم قبل: ' . number_format($balance - $value) . '
رصيد المستخدم بعد: ' . number_format($balance) . '
        ')->setParseMode('HTML');

        Bot::sendMessage($message->toArray());

    }
}
