<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PaymentMethodInfo extends Model
{
    use HasFactory;

    protected $guarded = [];
    protected $casts = ['is_active' => 'boolean'];

    public function paymentMethod()
    {
        return $this->belongsTo(PaymentMethod::class);
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
