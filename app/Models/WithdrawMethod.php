<?php

namespace App\Models;

use App\Enums\CommessionType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WithdrawMethod extends Model
{
    use HasFactory;
    protected $guarded = [];

    protected $casts = [
        'is_active' => 'boolean',
        'commession_type' => CommessionType::class
    ];

    public function withdrawInfos()
    {
        return $this->hasMany(WithdrawMethodInfo::class);
    }
}
