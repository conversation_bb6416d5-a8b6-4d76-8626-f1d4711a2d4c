<?php

namespace App\Models;

use App\Enums\CommessionType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WithdrawRequest extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $casts = [
        'is_charged' => 'boolean',
        'commession_type' => CommessionType::class,
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function withdrawMethod()
    {
        return $this->belongsTo(WithdrawMethod::class);
    }
}
