<?php

namespace App\Models;

use App\Helpers\ChargeRequestStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ChargeRequest extends Model
{
    use HasFactory;
    protected $guarded = [];

    protected $casts = [
        'is_charged' => 'boolean',
        'status' => ChargeRequestStatus::class
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function paymentMethod()
    {
        return $this->belongsTo(PaymentMethod::class);
    }
}
