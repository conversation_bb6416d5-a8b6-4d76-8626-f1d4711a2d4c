<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Instruction extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function items()
    {
        return $this->hasMany(InstructionItem::class);
    }
    public function instructionCategory()
    {
        return $this->belongsTo(InstructionCategory::class);
    }
}
