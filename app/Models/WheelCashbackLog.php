<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WheelCashbackLog extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'wheel_prize_id',
        'percentage',
        'base_amount',
        'cashback_amount',
        'is_processed',
        'processed_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'percentage' => 'float',
        'base_amount' => 'float',
        'cashback_amount' => 'float',
        'is_processed' => 'boolean',
        'processed_at' => 'datetime',
    ];

    /**
     * Get the user that owns the wheel cashback log.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the wheel prize that owns the wheel cashback log.
     */
    public function wheelPrize()
    {
        return $this->belongsTo(WheelPrize::class);
    }
}
