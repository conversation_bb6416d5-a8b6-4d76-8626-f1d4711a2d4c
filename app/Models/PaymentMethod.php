<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PaymentMethod extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $casts = [
        'is_active' => 'boolean',
        'enable_cashback' => 'boolean',
    ];

    public function paymentInfos()
    {
        return $this->hasMany(PaymentMethodInfo::class);
    }
    public function paymentInfo()
    {
        return $this->hasOne(PaymentMethodInfo::class)->latestOfMany()
            ->where('is_active', true);
    }
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
    public function paymentMessages()
    {
        return $this->hasMany(PaymentMessage::class);
    }
    public function instructions()
    {
        return $this->hasMany(PaymentInstruction::class);
    }
}
