<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ReferralItems extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $casts = [
        'created_at' => 'datetime',
        'sent_profit' => 'boolean'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
    public function referralCalculation()
    {
        return $this->belongsTo(ReferralCalculation::class);
    }
}
