<?php

namespace App\Jobs;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DatabaseBackupJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Log::info('Starting database backup process');

            // Get database configuration
            $connection = Config::get('database.default');
            $config = Config::get("database.connections.{$connection}");

            if (!$config) {
                throw new \Exception("Database connection '{$connection}' not found");
            }

            $appName = config('app.name', 'laravel');
            $backupPath = storage_path('app/' . $appName . '.sql');

            // Ensure the directory exists
            $directory = dirname($backupPath);
            if (!is_dir($directory)) {
                mkdir($directory, 0755, true);
            }

            // Check if mysqldump is available
            $mysqldumpAvailable = $this->isMysqldumpAvailable();

            if ($mysqldumpAvailable) {
                $this->createBackupWithMysqldump($config, $backupPath);
            } else {
                Log::warning('mysqldump not available, using Laravel-based backup method');
                $this->createBackupWithLaravel($backupPath);
            }

            // Verify the backup file was created and has content
            if (!file_exists($backupPath) || filesize($backupPath) === 0) {
                throw new \Exception('Backup file was not created or is empty');
            }

            $fileSize = round(filesize($backupPath) / 1024 / 1024, 2); // Size in MB
            Log::info("Database backup completed successfully. File size: {$fileSize} MB");

        } catch (\Exception $e) {
            Log::error('Database backup failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Check if mysqldump command is available
     */
    private function isMysqldumpAvailable(): bool
    {
        $output = [];
        $returnCode = 0;
        exec('which mysqldump 2>/dev/null', $output, $returnCode);
        return $returnCode === 0;
    }

    /**
     * Create backup using mysqldump command
     */
    private function createBackupWithMysqldump(array $config, string $backupPath): void
    {
        $host = $config['host'];
        $port = $config['port'];
        $database = $config['database'];
        $username = $config['username'];
        $password = $config['password'];

        $command = sprintf(
            'mysqldump --host=%s --port=%s --user=%s --password=%s --single-transaction --routines --triggers %s > %s',
            escapeshellarg($host),
            escapeshellarg($port),
            escapeshellarg($username),
            escapeshellarg($password),
            escapeshellarg($database),
            escapeshellarg($backupPath)
        );

        $output = [];
        $returnCode = 0;
        exec($command . ' 2>&1', $output, $returnCode);

        if ($returnCode !== 0) {
            $errorMessage = 'mysqldump failed. Output: ' . implode("\n", $output);
            Log::error($errorMessage);
            throw new \Exception($errorMessage);
        }
    }

    /**
     * Create backup using Laravel database queries
     */
    private function createBackupWithLaravel(string $backupPath): void
    {
        $tables = DB::select('SHOW TABLES');
        $databaseName = Config::get('database.connections.' . Config::get('database.default') . '.database');

        $sql = "-- Database backup created on " . date('Y-m-d H:i:s') . "\n";
        $sql .= "-- Database: {$databaseName}\n\n";

        $sql .= "SET FOREIGN_KEY_CHECKS=0;\n\n";

        foreach ($tables as $table) {
            $tableName = array_values((array) $table)[0];

            // Get CREATE TABLE statement
            $createTable = DB::select("SHOW CREATE TABLE `{$tableName}`");
            $sql .= "-- Table structure for table `{$tableName}`\n";
            $sql .= "DROP TABLE IF EXISTS `{$tableName}`;\n";
            $sql .= $createTable[0]->{'Create Table'} . ";\n\n";

            // Get table data
            $rows = DB::table($tableName)->get();
            if ($rows->count() > 0) {
                $sql .= "-- Dumping data for table `{$tableName}`\n";
                $sql .= "INSERT INTO `{$tableName}` VALUES\n";

                $values = [];
                foreach ($rows as $row) {
                    $rowData = array_map(function ($value) {
                        return $value === null ? 'NULL' : "'" . addslashes($value) . "'";
                    }, (array) $row);
                    $values[] = '(' . implode(',', $rowData) . ')';
                }

                $sql .= implode(",\n", $values) . ";\n\n";
            }
        }

        $sql .= "SET FOREIGN_KEY_CHECKS=1;\n";

        file_put_contents($backupPath, $sql);
    }
}
