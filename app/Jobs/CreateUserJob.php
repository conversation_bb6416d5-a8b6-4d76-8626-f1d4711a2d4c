<?php

namespace App\Jobs;

use App\BotApi\Connectors\PythonBotConnector;
use App\BotApi\Requests\PythonBot\CreateUserRequest;
use App\Facades\Bot;
use App\Facades\BotMessage;
use App\Services\UserService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class CreateUserJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(protected array $data, protected string $userTelegramId)
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $userService = app()->make(UserService::class);
        $user = $userService->getByTelegramId($this->userTelegramId);
        if (!$user) return;
        $connector = new PythonBotConnector();
        $request = new CreateUserRequest($this->data);
        $response = $connector->send($request);
        if ($response->successful()) {
            $text = "تم إنشاء الحساب بنجاح
    *اسم المستخدم*: `" . $this->data['username'] . "`
    *كلمة المرور*: `" . $this->data['password'] . "`";
            $user->ichancyAccount()->create([
                'username' => $this->data['username'],
                'password' => $this->data['password'],
            ]);
            $message = BotMessage::setChat($this->userTelegramId)
                ->setText($text)
                ->toArray();
            Bot::sendMessage($message);
            return;
        }
        $message = BotMessage::setChat($this->userTelegramId)
            ->setText('فشل أنشاء الحساب, يرجى المحاولة في وقت لاحق')
            ->toArray();
        Bot::sendMessage($message);
    }
}
