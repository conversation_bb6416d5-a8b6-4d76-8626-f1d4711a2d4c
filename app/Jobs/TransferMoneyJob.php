<?php

namespace App\Jobs;

use App\BotApi\Connectors\PythonBotConnector;
use App\BotApi\Enum\MoneyTransferType;
use App\BotApi\Requests\PythonBot\ChargeRequest;
use App\BotApi\Requests\PythonBot\WithdrawRequest;
use App\Facades\Bot;
use App\Facades\BotMessage;
use App\Services\UserService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class TransferMoneyJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(
        protected MoneyTransferType $type,
        protected string $amount,
        protected string $userTelegramId
    ) {}

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $userService = app()->make(UserService::class);
        $user = $userService->getByTelegramId($this->userTelegramId);
        if (!$user) return;

        $connector = new PythonBotConnector();
        if ($this->type == MoneyTransferType::WITHDRAW) {
            $request = new WithdrawRequest([
                'username' => $user->ichancyAccount->username,
                'amount' => $this->amount
            ]);
            $response = $connector->send($request);
        } else {
            $request = new ChargeRequest([
                'username' => $user->ichancyAccount->username,
                'amount' => $this->amount
            ]);
            $response = $connector->send($request);
        }
        if ($response->successful()) {
            $balance = $user->userBalance()->firstOrCreate();
            if ($this->type == MoneyTransferType::WITHDRAW) {
                $balance->active += $this->amount;
            } else {
                $balance->active -= $this->amount;
            }
            $balance->save();

            $message = BotMessage::setChat($this->userTelegramId)
                ->setText('تم تحويل المبلغ بنجاح')
                ->toArray();
            Bot::sendMessage($message);
            return;
        }
        $data = $response->json();
        $text = isset($data['message']) ? $data['message'] : 'فشل تحويل المبلغ, يرجى المحاولة في وقت لاحق';
        $message = BotMessage::setChat($this->userTelegramId)
            ->setText($text)
            ->toArray();
        Bot::sendMessage($message);
    }
}
